{"version": "6", "dialect": "sqlite", "id": "54a8da79-3d04-4721-82ab-4d0cd2cf5e7d", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"employee": {"name": "employee", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "middle_name": {"name": "middle_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date_of_birth": {"name": "date_of_birth", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'Male'"}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "passport_number": {"name": "passport_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "marital_status": {"name": "marital_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address1": {"name": "address1", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address2": {"name": "address2", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address3": {"name": "address3", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address4": {"name": "address4", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "postcode": {"name": "postcode", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'England'"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email_type": {"name": "email_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Personal'"}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone_type": {"name": "phone_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Mobile'"}, "additional_emails": {"name": "additional_emails", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "additional_phones": {"name": "additional_phones", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "emergency_contacts": {"name": "emergency_contacts", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "works_number": {"name": "works_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "departments": {"name": "departments", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_protected_under_tupe": {"name": "is_protected_under_tupe", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "leave_date": {"name": "leave_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payroll_id": {"name": "payroll_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "change_of_payroll_id": {"name": "change_of_payroll_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contracted_hours": {"name": "contracted_hours", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "irregular_payment_pattern": {"name": "irregular_payment_pattern", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "non_individual": {"name": "non_individual", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "leave_year_starts": {"name": "leave_year_starts", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_leave_calculation_method": {"name": "annual_leave_calculation_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_leave_entitlement": {"name": "annual_leave_entitlement", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_leave_carry_over": {"name": "annual_leave_carry_over", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "annual_leave_carry_over_value": {"name": "annual_leave_carry_over_value", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "annual_leave_carry_over_unit": {"name": "annual_leave_carry_over_unit", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'days'"}, "annual_leave_adjustment": {"name": "annual_leave_adjustment", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "annual_leave_adjustment_value": {"name": "annual_leave_adjustment_value", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "annual_leave_adjustment_unit": {"name": "annual_leave_adjustment_unit", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'days'"}, "working_days": {"name": "working_days", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "overseas_employer": {"name": "overseas_employer", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "starter_declaration": {"name": "starter_declaration", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "previous_employment_pay": {"name": "previous_employment_pay", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "previous_employment_tax": {"name": "previous_employment_tax", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_frequency": {"name": "payment_frequency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pay_calculation_method": {"name": "pay_calculation_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "period_pay_rate": {"name": "period_pay_rate", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_salary": {"name": "annual_salary", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "starting_salary": {"name": "starting_salary", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "hourly_rates": {"name": "hourly_rates", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "daily_rates": {"name": "daily_rates", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_details": {"name": "payment_details", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "minimum_wage_profile": {"name": "minimum_wage_profile", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "typical_hours_worked": {"name": "typical_hours_worked", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "zero_pay_handling": {"name": "zero_pay_handling", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tax_code": {"name": "tax_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "week1_month1": {"name": "week1_month1", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "ni_table": {"name": "ni_table", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "employer_ni_contributions": {"name": "employer_ni_contributions", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ni_number": {"name": "ni_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_director": {"name": "is_director", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "director_start_date": {"name": "director_start_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "director_end_date": {"name": "director_end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "director_ni_calc_method": {"name": "director_ni_calc_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'standard'"}, "is_off_payroll_worker": {"name": "is_off_payroll_worker", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "trivial_commutation": {"name": "trivial_commutation", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "flexible_drawdown": {"name": "flexible_drawdown", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "student_loan": {"name": "student_loan", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "student_loan_start_date": {"name": "student_loan_start_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "student_loan_end_date": {"name": "student_loan_end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "postgraduate_loan": {"name": "postgraduate_loan", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pg_loan_start_date": {"name": "pg_loan_start_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pg_loan_end_date": {"name": "pg_loan_end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "next_review_date": {"name": "next_review_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "medical": {"name": "medical", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_confidential": {"name": "is_confidential", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "enable_self_service": {"name": "enable_self_service", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "pdf_password": {"name": "pdf_password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "employer": {"name": "employer", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "trading_name": {"name": "trading_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address1": {"name": "address1", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address2": {"name": "address2", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address3": {"name": "address3", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address4": {"name": "address4", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "postcode": {"name": "postcode", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "office_number": {"name": "office_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paye_reference": {"name": "paye_reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "accounts_office_reference": {"name": "accounts_office_reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "hmrc_office": {"name": "hmrc_office", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "small_employers_relief": {"name": "small_employers_relief", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "company_registration_number": {"name": "company_registration_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "unique_tax_reference": {"name": "unique_tax_reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "corporation_tax_reference": {"name": "corporation_tax_reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "bacs_sun": {"name": "bacs_sun", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sender_type": {"name": "sender_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Employer'"}, "sender_id": {"name": "sender_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_title_rti": {"name": "contact_title_rti", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Mr'"}, "contact_first_name_rti": {"name": "contact_first_name_rti", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_last_name_rti": {"name": "contact_last_name_rti", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_email_rti": {"name": "contact_email_rti", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_phone_rti": {"name": "contact_phone_rti", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_fax_rti": {"name": "contact_fax_rti", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "typical_pay_frequency": {"name": "typical_pay_frequency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Monthly'"}, "typical_pay_basis": {"name": "typical_pay_basis", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Salaried'"}, "typical_pay_method": {"name": "typical_pay_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'DirectDeposit'"}, "typical_leave_year_start": {"name": "typical_leave_year_start", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'January'"}, "typical_leave_calculation_method": {"name": "typical_leave_calculation_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Days'"}, "typical_leave_entitlement": {"name": "typical_leave_entitlement", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 28}, "typical_working_days": {"name": "typical_working_days", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "typical_hours_worked": {"name": "typical_hours_worked", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "typical_minimum_wage_profile": {"name": "typical_minimum_wage_profile", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Standard'"}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_email": {"name": "contact_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "alt_contact_name": {"name": "alt_contact_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "alt_contact_email": {"name": "alt_contact_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "alt_contact_phone": {"name": "alt_contact_phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "filing_types": {"name": "filing_types", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "client_notes": {"name": "client_notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payroll": {"name": "payroll", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "employee_id": {"name": "employee_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "basic_section": {"name": "basic_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "additions_section": {"name": "additions_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "deductions_section": {"name": "deductions_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "statutory_section": {"name": "statutory_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pension_section": {"name": "pension_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes_section": {"name": "notes_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "summary_section": {"name": "summary_section", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "period_start": {"name": "period_start", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "period_end": {"name": "period_end", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pay_date": {"name": "pay_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}