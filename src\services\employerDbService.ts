// Service layer for all employer DB operations (renderer process)
// All functions use Electron IPC to call the main process

export async function getPayPeriods(dbPath: string) {
  console.log("[Service] getPayPeriods called with dbPath:", dbPath);
  const result = await window.api.invoke("employerDb:getPayPeriods", dbPath);
  console.log("[Service] getPayPeriods result:", result);
  if (result.success) return result.payPeriods;
  throw new Error(result.error || "Failed to fetch pay periods");
}

export async function insertSinglePayPeriod(dbPath: string, payPeriod: any) {
  const result = await window.api.invoke("employerDb:addPayPeriod", dbPath, payPeriod);
  if (result.success) return result.payPeriod;
  throw new Error(result.error || "Failed to add pay period");
}

// Add multiple pay periods (batch)
export async function insertMultiplePayPeriods(dbPath: string, payPeriods: any[]) {
  const result = await window.api.invoke("employerDb:addPayPeriods", dbPath, payPeriods);
  if (result.success) return result.payPeriods;
  throw new Error(result.error || "Failed to add pay periods");
}


export async function updatePayPeriod(dbPath: string, payPeriod: any) {
  const result = await window.api.invoke("employerDb:updatePayPeriod", dbPath, payPeriod);
  if (result.success) return result.payPeriod;
  throw new Error(result.error || "Failed to update pay period");
}

export async function deletePayPeriod(dbPath: string, id: string) {
  const result = await window.api.invoke("employerDb:deletePayPeriod", dbPath, id);
  if (result.success) return id;
  throw new Error(result.error || "Failed to delete pay period");
}

export async function openEmployerDb(
  dbPath: string,
): Promise<{ success: boolean; error?: string }> {
  // window.api.invoke is the standard IPC bridge (preload script must expose this)
  return await window.api.invoke("employerDb:openPersistent", dbPath);
}

// Example: get payrolls for an employer (add more as needed)
export async function getPayrolls(employerId: string): Promise<any[]> {
  return await window.api.invoke("employerDb:getPayrolls", employerId);
}

import type { Employee } from "@/lib/schemas/employee";

export async function getEmployees(dbPath: string): Promise<Employee[]> {
  const result = await window.api.invoke("employerDb:getEmployees", dbPath);
  console.log('[getEmployees] IPC result:', result);
  if (result.success) {
    return result.employees;
  }
  throw new Error(result.error || "Failed to fetch employees");
}

// Add a new employee to the employer DB
export async function addEmployee(dbPath: string, employee: Employee): Promise<Employee> {
  // Assign a new UUID if id is missing or empty
  if (!employee.id || employee.id === '') {
    employee.id = crypto.randomUUID();
  }
  const result = await window.api.invoke("employerDb:addEmployee", dbPath, employee);
  if (result.success) {
    return result.employee;
  }
  throw new Error(result.error || "Failed to add employee");
}

// Update an existing employee in the employer DB
export async function updateEmployee(dbPath: string, employee: Employee): Promise<Employee> {
  const result = await window.api.invoke("employerDb:updateEmployee", dbPath, employee);
  if (result.success) {
    return result.employee;
  }
  throw new Error(result.error || "Failed to update employee");
}

// Delete all pay periods by type and name (schedule)
export async function deletePayPeriodsByTypeAndName(dbPath: string, type: string, name: string|null) {
  const result = await window.api.invoke("employerDb:deletePayPeriodsByTypeAndName", dbPath, type, name);
  if (result.success) return result.deletedCount;
  throw new Error(result.error || "Failed to delete pay periods for schedule");
}

// Add more DB operations as needed
