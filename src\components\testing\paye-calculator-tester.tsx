'use client';

import { useState, useEffect } from 'react';
import { calculatePaye } from '@/core/calculations/paye/calculator';
import { PayPeriodType } from '@/core/calculations/paye/types';
import { TaxYear2023_2024 } from '@/core/calculations/tax-years/tax-year-2023-2024';
import { TaxYear2024_2025 } from '@/core/calculations/tax-years/tax-year-2024-2025';

// Define tax year options
const taxYearOptions = [
  { value: '2023-2024', label: '2023-2024', config: TaxYear2023_2024 },
  { value: '2024-2025', label: '2024-2025', config: TaxYear2024_2025 }
];

// Define period type options
const periodTypeOptions = [
  { value: PayPeriodType.MONTHLY, label: 'Monthly' },
  { value: PayPeriodType.WEEKLY, label: 'Weekly' },
  { value: PayPeriodType.FOUR_WEEKLY, label: '4-Weekly' },
  { value: PayPeriodType.ANNUALLY, label: 'Annual' }
];

// Pay period conversion factors (relative to annual)
const payPeriodFactors = {
  [PayPeriodType.WEEKLY]: 52,
  [PayPeriodType.TWO_WEEKLY]: 26,
  [PayPeriodType.FOUR_WEEKLY]: 13,
  [PayPeriodType.MONTHLY]: 12,
  [PayPeriodType.QUARTERLY]: 4,
  [PayPeriodType.BI_ANNUALLY]: 2,
  [PayPeriodType.ANNUALLY]: 1
};

export function PayeCalculatorTester() {
  // Form state
  const [formValues, setFormValues] = useState({
    taxYear: '2023-2024',
    periodType: PayPeriodType.MONTHLY,
    periodNumber: 1,
    taxCode: '1257L',
    isNonCumulative: false,
    isScottishTaxpayer: false,
    isWelshTaxpayer: false,
    grossPayToDate: 0,
    taxToDate: 0,
    grossPay: 2500,
    // Store the annual equivalent of gross pay to use when converting between period types
    annualizedGrossPay: 30000
  });

  // Results state
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [showIntermediateValues, setShowIntermediateValues] = useState(false);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    // Handle different input types
    const newValue = type === 'checkbox' 
      ? checked 
      : type === 'number' 
        ? parseFloat(value) || 0
        : value;
    
    const updatedValues = {
      ...formValues,
      [name]: newValue
    };

    // If gross pay is changed, update the annualized value
    if (name === 'grossPay') {
      const grossPay = parseFloat(value) || 0;
      updatedValues.annualizedGrossPay = grossPay * payPeriodFactors[formValues.periodType];
    }
    
    // Handle taxpayer status mutual exclusivity
    if (name === 'isScottishTaxpayer' && checked) {
      updatedValues.isWelshTaxpayer = false;
    } else if (name === 'isWelshTaxpayer' && checked) {
      updatedValues.isScottishTaxpayer = false;
    }
    
    setFormValues(updatedValues);
  };

  // Handle period type selection
  const handlePeriodTypeChange = (periodType: PayPeriodType) => {
    // Calculate new gross pay based on the period type
    const factor = payPeriodFactors[periodType];
    const newGrossPay = Math.round((formValues.annualizedGrossPay / factor) * 100) / 100;
    
    setFormValues({
      ...formValues,
      periodType,
      grossPay: newGrossPay
    });
  };

  // Handle tax year selection
  const handleTaxYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormValues({
      ...formValues,
      taxYear: e.target.value
    });
  };

  // Calculate PAYE
  const calculateTax = () => {
    try {
      setError(null);
      
      const result = calculatePaye({
        taxCode: formValues.taxCode,
        grossPay: formValues.grossPay,
        payPeriod: formValues.periodType,
        periodNumber: formValues.periodNumber,
        previousPayToDate: formValues.grossPayToDate,
        previousTaxDue: formValues.taxToDate,
        isNonCumulative: formValues.isNonCumulative,
        isScottishTaxpayer: formValues.isScottishTaxpayer,
        isWelshTaxpayer: formValues.isWelshTaxpayer,
        taxYearConfig: formValues.taxYear === '2023-2024' ? TaxYear2023_2024 : TaxYear2024_2025
      });
      
      setResults(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setResults(null);
    }
  };

  // Helper function to safely format numbers
  const safeFormat = (value: any, decimals = 2) => {
    if (value === undefined || value === null) return '0.00';
    return typeof value === 'number' ? value.toFixed(decimals) : '0.00';
  };

  // Calculate actual vs expected tax values for specific test cases (for debugging)
  const calculateTestDiscrepancy = () => {
    if (!results) return null;
    
    // Basic test case calculations
    const rawTaxDue = results.grossPay * 0.2; // Simple 20% tax calculation
    const discrepancy = results.taxDue - rawTaxDue;
    
    return {
      raw: rawTaxDue,
      calculated: results.taxDue,
      difference: discrepancy,
      percentageDiff: (discrepancy / rawTaxDue) * 100
    };
  };

  // Get discrepancy data for debugging
  const discrepancy = calculateTestDiscrepancy();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Input Form */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Input Parameters</h2>
        
        <div className="space-y-4">
          {/* Tax Year */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tax Year
            </label>
            <select
              name="taxYear"
              value={formValues.taxYear}
              onChange={handleTaxYearChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {taxYearOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          {/* Period Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Period Type
            </label>
            <div className="flex flex-wrap gap-2">
              {periodTypeOptions.map(option => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => handlePeriodTypeChange(option.value)}
                  className={`px-3 py-1 text-sm rounded-md ${
                    formValues.periodType === option.value
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-800'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Note: Changing period type will adjust the gross pay accordingly
            </p>
          </div>
          
          {/* Period Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Period Number
            </label>
            <input
              type="number"
              name="periodNumber"
              value={formValues.periodNumber}
              onChange={handleInputChange}
              min="1"
              max="56"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          {/* Tax Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tax Code
            </label>
            <input
              type="text"
              name="taxCode"
              value={formValues.taxCode}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="e.g. 1257L, BR, D0, K100, S1257L"
            />
            <p className="text-xs text-gray-500 mt-1">
              Use &apos;S&apos; prefix for Scottish codes or check Scottish taxpayer below
            </p>
          </div>
          
          {/* Taxpayer Status Options */}
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isScottishTaxpayer"
                id="isScottishTaxpayer"
                checked={formValues.isScottishTaxpayer}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="isScottishTaxpayer" className="ml-2 text-sm text-gray-700">
                Scottish Taxpayer
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isWelshTaxpayer"
                id="isWelshTaxpayer"
                checked={formValues.isWelshTaxpayer}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="isWelshTaxpayer" className="ml-2 text-sm text-gray-700">
                Welsh Taxpayer
              </label>
            </div>
          </div>
          
          {/* Week 1/Month 1 Basis */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="isNonCumulative"
              id="isNonCumulative"
              checked={formValues.isNonCumulative}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 rounded"
            />
            <label htmlFor="isNonCumulative" className="ml-2 text-sm text-gray-700">
              Week 1/Month 1 Basis (Non-Cumulative)
            </label>
          </div>
          
          {/* Gross Pay To Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Gross Pay To Date (£)
            </label>
            <input
              type="number"
              name="grossPayToDate"
              value={formValues.grossPayToDate}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          {/* Tax To Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tax To Date (£)
            </label>
            <input
              type="number"
              name="taxToDate"
              value={formValues.taxToDate}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          {/* Gross Pay (This Period) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Gross Pay - This Period (£)
            </label>
            <input
              type="number"
              name="grossPay"
              value={formValues.grossPay}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
            <p className="text-xs text-gray-500 mt-1">
              Annual equivalent: £{(formValues.grossPay * payPeriodFactors[formValues.periodType]).toFixed(2)}
            </p>
          </div>
          
          {/* Calculate Button */}
          <button
            type="button"
            onClick={calculateTax}
            className="w-full bg-slate-800 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Calculate PAYE
          </button>
          
          {/* Debug Options */}
          <div className="flex items-center mt-4">
            <input
              type="checkbox"
              id="showIntermediateValues"
              checked={showIntermediateValues}
              onChange={() => setShowIntermediateValues(!showIntermediateValues)}
              className="h-4 w-4 text-blue-600 rounded"
            />
            <label htmlFor="showIntermediateValues" className="ml-2 text-xs text-gray-700">
              Show 4-decimal intermediate values (for debugging)
            </label>
          </div>
        </div>
      </div>
      
      {/* Results Panel */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Calculation Results</h2>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p className="font-bold">Error</p>
            <p>{error}</p>
          </div>
        )}
        
        {results && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Gross Pay</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.grossPay)}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Tax Due</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.taxDue)}</p>
                {showIntermediateValues && (
                  <p className="text-xs text-gray-500">
                    (Raw: £{results?.taxDue?.toFixed(4)})
                  </p>
                )}
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Tax Free Amount</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.taxFreeAmount)}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Taxable Pay</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.taxablePay)}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Effective Tax Rate</p>
                <p className="text-lg font-semibold">{safeFormat(results?.effectiveTaxRate, 2)}%</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Tax Code Used</p>
                <p className="text-lg font-semibold">{results?.taxCodeUsed || 'N/A'}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Cumulative Gross Pay</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.cumulativeGrossPay)}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Cumulative Tax Due</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.cumulativeTaxDue)}</p>
                {showIntermediateValues && (
                  <p className="text-xs text-gray-500">
                    (Raw: £{results?.cumulativeTaxDue?.toFixed(4)})
                  </p>
                )}
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Cumulative Taxable Pay</p>
                <p className="text-lg font-semibold">£{safeFormat(results?.cumulativeTaxablePay)}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Emergency Tax Applied</p>
                <p className="text-lg font-semibold">{results?.emergencyTaxApplied ? 'Yes' : 'No'}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">Regulatory Limit Applied</p>
                <p className="text-lg font-semibold">{results?.regulatoryLimitApplied ? 'Yes' : 'No'}</p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-500">PA Reduction Applied</p>
                <p className="text-lg font-semibold">{results?.personalAllowanceReductionApplied ? 'Yes' : 'No'}</p>
              </div>
            </div>
            
            {/* Tax Breakdown */}
            {results?.taxBreakdown && results.taxBreakdown.length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold mb-2">Tax Breakdown</h3>
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="text-left p-2 border">Band</th>
                      <th className="text-right p-2 border">Amount (£)</th>
                      <th className="text-right p-2 border">Rate (%)</th>
                      <th className="text-right p-2 border">Tax (£)</th>
                    </tr>
                  </thead>
                  <tbody>
                    {results.taxBreakdown.map((band: any, index: number) => (
                      <tr key={index} className="border-b">
                        <td className="p-2 border">{band.bandName}</td>
                        <td className="text-right p-2 border">{safeFormat(band.amount)}</td>
                        <td className="text-right p-2 border">{safeFormat(band.rate, 0)}%</td>
                        <td className="text-right p-2 border">{safeFormat(band.taxDue)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            {/* Debug Information */}
            {showIntermediateValues && discrepancy && (
              <div className="mt-6 border-t pt-4">
                <h3 className="font-semibold mb-2">Debug Information</h3>
                <div className="text-xs text-gray-600 space-y-1">
                  <p>
                    Raw calculation: £{safeFormat(discrepancy.raw, 4)} (Gross × Rate)
                  </p>
                  <p>
                    Calculated tax: £{safeFormat(discrepancy.calculated, 4)} (With 4dp precision)
                  </p>
                  <p>
                    Difference: £{safeFormat(discrepancy.difference, 4)} 
                    ({safeFormat(discrepancy.percentageDiff, 6)}%)
                  </p>
                  <p className="text-gray-500 mt-2 italic">
                    Note: Differences are expected due to PAYErout rounding rules
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
