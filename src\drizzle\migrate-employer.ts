import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as fs from 'fs';
import * as path from 'path';

// Run all SQL migrations in migrations/employer/ on the target employer DB
export async function runMigrationsEmployer(dbPath: string) {
  const client = createClient({ url: `file:${dbPath}` });
  const db = drizzle(client);

  // Try dist-electron/migrations/employer first, then src/drizzle/migrations/employer
  // Add logging for each candidate migrations directory and use the first one that exists
  const candidateDirs = [
    path.resolve(__dirname, '../migrations/employer'), // dist-electron/migrations/employer
    path.resolve(__dirname, '../../../src/drizzle/migrations/employer') // src/drizzle/migrations/employer
  ];
  let migrationsDir = '';
  for (const dir of candidateDirs) {
    console.log('[MIGRATION] Checking migrations dir:', dir);
    if (fs.existsSync(dir)) {
      migrationsDir = dir;
      console.log('[MIGRATION] Using migrations dir:', migrationsDir);
      break;
    }
  }
  if (!migrationsDir) {
    throw new Error(
      `[MIGRATION] Could not find employer migrations directory. Checked: ${candidateDirs.join(', ')}`
    );
  }
  const files = fs.readdirSync(migrationsDir).filter(f => f.endsWith('.sql'));
  for (const file of files) {
    const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
    // Split on semicolon to avoid issues with multiple statements
    const statements = sql.split(';').map(s => s.trim()).filter(Boolean);
    for (const stmt of statements) {
      if (stmt) {
        await db.run(stmt);
      }
    }
  }
  await client.close();
}
