import { employer as employerTable } from "./employer/employer.schema";
import { employee as employeeTable } from "./employer/employee.schema";
import { payPeriods as payPeriodTable } from "./employer/payPeriod";

// Re-export individual tables for Drizzle Kit discovery
export const employer = employerTable;
export const employee = employeeTable;
export const payPeriod = payPeriodTable;

// Keep the grouped export if it's used elsewhere
export const employerDbSchema = {
  employer: employerTable,
  employee: employeeTable,
  payPeriod: payPeriodTable,
};
