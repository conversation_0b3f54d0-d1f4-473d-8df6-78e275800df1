{"name": "uk-payroll", "description": "UK Payroll Software", "author": "A1 Payroll", "version": "0.1.0", "private": true, "main": "dist-electron/electron/main/main.js", "scripts": {"start": "npx serve@latest out", "lint": "next lint", "dev:next": "next dev --turbo", "clean:electron": "rimraf dist-electron tsconfig.electron.tsbuildinfo", "generate:migrations": "drizzle-kit generate --config=drizzle.master.config.ts", "copy:migrations": "copyfiles -u 3 \"src/drizzle/migrations/**/*\" \"src/drizzle/migrations/**/.*\" dist-electron/migrations", "build:electron:ts": "npm run clean:electron && npm run generate:migrations && tsc -p tsconfig.electron.json && npm run copy:migrations", "dev:electron": "npm run build:electron:ts && cross-env NODE_ENV=development electron ./dist-electron/electron/main/main.js", "dev": "concurrently \"npm run dev:next\" \"wait-on http://localhost:3000 && npm run dev:electron\"", "build:next": "next build", "build:electron": "cross-env NODE_ENV=production electron-builder build --config electron-builder.json --win --publish never", "build": "npm run build:next && npm run build:electron", "test-db": "ts-node test-db.ts", "migrate:employer": "drizzle-kit generate --config ./drizzle.config.ts", "migrate:master": "drizzle-kit generate --config=drizzle.master.config.ts && tsx src/drizzle/run-migrations.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@libsql/client": "^0.15.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.2", "@types/uuid": "^10.0.0", "animejs": "^4.0.1", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "date-fns": "3.3.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "drizzle-zod": "^0.7.1", "electron": "^35.2.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.7.3", "init": "^0.1.2", "input-otp": "^1.4.2", "lucide-react": "^0.485.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "shadcn": "^2.4.0-canary.17", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss-animated": "^2.0.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "wait-on": "^8.0.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@electron/rebuild": "^3.6.0", "@eslint/eslintrc": "^3", "@mapbox/node-pre-gyp": "1.0.11", "@tailwindcss/postcss": "^4.1.3", "@types/node": "^20", "@types/path-browserify": "^1.0.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "10.4.17", "copyfiles": "^2.4.1", "drizzle-kit": "^0.31.0", "electron-builder": "^26.0.12", "eslint": "^9", "eslint-config-next": "15.2.4", "glob": "^10.3.10", "node-gyp": "9.4.1", "postcss": "8.4.35", "postcss-import": "16.0.0", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "rimraf": "^5.0.5", "tailwindcss": "^4.0.17", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3"}}