# UK Payroll Application - Development Task List

This document tracks development progress for the UK Payroll Application. Check off tasks as they are completed.

## Phase 1: Core Infrastructure (Weeks 1-4)

### Project Setup

- [x] Initialize Next.js project with TypeScript configuration
- [x] Add Electron integration
- [x] Configure Tailwind CSS and Shadcn UI
- [x] Set up project structure according to technical guide
- [ ] Configure build and development scripts

### Database Foundation

- [x] Set up Drizzle ORM with better-sqlite3
- [ ] Implement master database schema
- [ ] Implement employer database schema
- [ ] Create Zod validation schemas using drizzle-zod
- [ ] Set up database migration system

### Database Adapters

- [ ] Create master database adapter
- [ ] Create employer database adapter
- [ ] Implement database connection management
- [ ] Create factory methods for database instantiation
- [ ] Implement error handling for database operations

### File Management

- [x] Configure custom file extension (.ukpayroll)
- [ ] Implement file handling in Electron
- [ ] Create backup and restore functionality
- [ ] Implement file integrity validation
- [ ] Set up proper error handling for file operations

### Electron Setup

- [x] Set up Electron main process
- [x] Create preload scripts
- [x] Implement contextBridge for secure API exposure
- [x] Set up IPC communication handlers
- [ ] Configure file association handling

### TanStack Query Integration

- [x] Configure TanStack Query provider
- [x] Set up query defaults with proper configuration
- [x] Implement query client
- [x] Create query error handling strategy
- [x] Add React Query Devtools for development

## Phase 2: Employer Data Integration (Weeks 5-8)

### Next Step
- Begin connecting UI in all employer sections to the employer database (Drizzle ORM, better-sqlite3)
- Remove mock data from payroll overview and related components

### Dashboard Implementation

- [x] Create dashboard layout
- [ ] Implement recent employers list with TanStack Query
- [ ] Create employer card component
- [ ] Implement dashboard actions (create, open, import)
- [ ] Create loading states and error handling

### Custom Hooks

- [ ] Implement useMasterDatabase hook
- [ ] Create useEmployerDatabase hook
- [ ] Create TanStack Query employer hooks
- [ ] Implement useElectron hook for desktop features
- [ ] Create useCalculation hook for payroll calculations

### Employer Management

- [ ] Create employer creation workflow with TanStack Query mutations
- [ ] Implement employer editing functionality
- [ ] Build employer settings page
- [ ] Create employer deletion/archiving
- [ ] Implement employer metadata management

### File System Integration

- [ ] Implement file association handling
- [ ] Create file open/save dialogs
- [ ] Build recent files management
- [ ] Implement directory scanning functionality
- [ ] Create file event listeners

### Import/Export

- [ ] Create employer file import functionality
- [ ] Implement employer export capability
- [ ] Build CSV import for employees
- [ ] Create data validation for imports
- [ ] Implement progress indicators for long operations
- [2025-05-22] Scaffolded TanStack Query hooks for pay periods and employees. Scaffolded Payroll wizard modal and PayrollSection logic for pay frequency detection and wizard launch.

## Phase 3: Employee Management (Weeks 9-12)

### Employee List

- [x] Create employee list page with TanStack Table
- [x] Implement filtering and sorting
- [x] Create employee card component
- [ ] Implement bulk operations (select multiple)

### Employee Details

- [x] Create employee details form
- [x] Implement personal details section
- [x] Build employment details section
- [x] Create tax and payroll details section
- [ ] Implement bank details with encryption

### Employee CRUD

- [ ] Implement employee creation with TanStack Query
- [ ] Create employee updating mutations
- [ ] Build employee archiving/deletion
- [ ] Implement history tracking
- [ ] Create form validation with Zod

### Data Import

- [ ] Create employee import from CSV
- [ ] Implement data validation for imports
- [ ] Build import preview
- [ ] Create error reporting for failed imports
- [ ] Implement conflict resolution

### TanStack Query Optimization

- [ ] Implement query keys strategy
- [ ] Create query invalidation patterns
- [ ] Build optimistic updates for mutations
- [ ] Implement prefetching for common navigation patterns
- [ ] Create proper loading states

## Phase 4: Core Calculation Engine (Weeks 13-16)

### Tax Year Configuration

- [x] Create tax year definitions
- [x] Implement tax bands and rates
- [x] Set up National Insurance thresholds
- [x] Create student loan thresholds
- [ ] Implement statutory payment rates

### PAYE Calculations

- [x] Implement tax code parsing
- [x] Create free pay calculations
- [x] Implement tax bracket calculations
- [x] Build cumulative/non-cumulative handling
- [x] Create Scottish tax rate support

### National Insurance

- [x] Implement NI category handling
- [x] Create employee NI calculations
- [x] Implement employer NI calculations
- [ ] Build NI rebate handling
- [x] Create directors' NI calculations

### Deductions & Adjustments

- [ ] Implement student loan calculations
- [ ] Create pension calculations
- [ ] Build attachment of earnings processing
- [ ] Implement salary sacrifice handling
- [ ] Create statutory payment calculations

### Web Workers Implementation

- [ ] Set up Web Worker infrastructure
- [ ] Move calculation engine to Web Worker
- [ ] Implement progress reporting
- [ ] Create batched processing for efficiency
- [ ] Build fallbacks for testing

## Phase 5: Payroll Processing (Weeks 17-20)

### Pay Period Management

- [ ] Create pay period setup
- [ ] Implement pay calendars
- [ ] Build period configuration
- [ ] Create period locking mechanism
- [ ] Implement period rollover

### Payroll Run

- [ ] Create payroll run workflow
- [ ] Implement employee selection
- [ ] Build batch processing
- [ ] Create preview functionality
- [ ] Implement approvals process

### Payslip Generation

- [ ] Create payslip calculations
- [ ] Implement payslip template
- [ ] Build PDF generation
- [ ] Create email distribution (optional)
- [ ] Implement print functionality

### Reports

- [ ] Create summary reports
- [ ] Implement detailed reports
- [ ] Build custom report options
- [ ] Create export to Excel functionality
- [ ] Implement report scheduling (optional)

### Data Tables

- [ ] Implement TanStack Table
- [ ] Create virtualized data grids
- [ ] Build sorting and filtering
- [ ] Implement pagination
- [ ] Create data export from tables

## Phase 6: HMRC Integration (Weeks 21-24)

### HMRC API Integration

- [ ] Set up HMRC API client
- [ ] Implement OAuth authentication flow
- [ ] Create token management and refresh
- [ ] Build error handling and retry logic
- [ ] Implement offline operation queue

### RTI Submissions

- [ ] Implement FPS (Full Payment Submission)
- [ ] Create EPS (Employer Payment Summary)
- [ ] Build submission validation
- [ ] Implement submission tracking
- [ ] Create submission history and audit

### Validation & Compliance

- [ ] Create pre-submission validation
- [ ] Implement schema compliance checks
- [ ] Build business rule validation
- [ ] Create error correction workflows
- [ ] Implement validation reporting

### Document Generation

- [ ] Set up React-PDF for document generation
- [ ] Create P60 templates
- [ ] Implement P45 generation
- [ ] Build custom letter templates
- [ ] Create document storage and retrieval

### Security & Audit

- [ ] Implement sensitive data encryption
- [ ] Create comprehensive audit logging
- [ ] Build user action tracking
- [ ] Implement data access controls
- [ ] Create security reporting

## Phase 7: Desktop Refinement (Weeks 25-28)

### User Interface Refinement

- [ ] Implement keyboard shortcuts
- [ ] Create context menus
- [ ] Build notification system
- [ ] Implement progress indicators
- [ ] Create help system

### Performance Optimization

- [ ] Profile and optimize database operations
- [ ] Implement efficient data loading strategies
- [ ] Optimize calculation performance
- [ ] Improve document generation speed
- [ ] Enhance UI responsiveness

### File System Integration

- [ ] Enhance file association handling
- [ ] Implement deep linking
- [ ] Create automated backup system
- [ ] Build file recovery tools
- [ ] Implement file synchronization (optional)

### Installation & Updates

- [ ] Create installer package
- [ ] Implement auto-update mechanism
- [ ] Build update notification system
- [ ] Create crash reporting
- [ ] Implement telemetry (optional)

### Final Testing & Packaging

- [ ] Create comprehensive test suite
- [ ] Implement error tracking
- [ ] Build installer packages for all platforms
- [ ] Create documentation
- [ ] Prepare for distribution

## Future Tasks

### Authentication (Future)

- [ ] Implement user authentication system
- [ ] Create user roles and permissions
- [ ] Build multi-user support
- [ ] Implement license management
- [ ] Create device activation

### Cloud Synchronization (Future)

- [ ] Design sync architecture
- [ ] Implement conflict resolution
- [ ] Build selective sync capability
- [ ] Create background sync process
- [ ] Implement offline mode handling

### Portals (Future)

- [ ] Create employer portal web application
- [ ] Implement employee self-service portal
- [ ] Build secure data access controls
- [ ] Create document sharing capabilities
- [ ] Implement notification system

### Production Ready Tasks

- Remove automatic migration of employer DBs on open (see main.ts). This is for development only and should be removed before production deployment to avoid unintended schema changes in user databases.

- change hard coded file path for employer DB to use the file path set by the user (either on install or in settings). see this line in EmployerCreateModal.tsx:

```ts
 // Define employer directory in a single place for use throughout the component
 const employerDirectory = "a:/WEBSITES/A1 Payroll Software V2/uk-payroll/test-employers";
```
