import { useQuery } from '@tanstack/react-query';
import { useEmployerDBContext } from '@/providers/employer-db-provider';
import { getEmployees } from '@/services/employerDbService';
import type { Employee } from '@/lib/schemas/employee';

/**
 * TanStack Query hook for fetching employees from the active employer DB.
 * Returns { data, isLoading, error }.
 *
 * - Uses the active employer's dbPath from context
 * - Handles multi-employer support by using correct dbPath
 * - Returns an empty array if no employer is active
 */
export function useEmployeesQuery() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;

  return useQuery<Employee[], Error>({
    queryKey: ['employees', dbPath],
    queryFn: async () => {
      if (!dbPath) return [];
      return await getEmployees(dbPath);
    },
    enabled: !!dbPath,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
}
