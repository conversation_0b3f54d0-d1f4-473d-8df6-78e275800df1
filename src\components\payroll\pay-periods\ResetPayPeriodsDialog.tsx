import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";

interface ResetPayPeriodsDialogProps {
  open: boolean;
  periodType: string | null;
  periodName: string | null;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onReset: () => void;
}

export function ResetPayPeriodsDialog({
  open,
  periodType,
  periodName,
  onOpenChange,
  onCancel,
  onReset
}: ResetPayPeriodsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-red-600">Reset Pay Periods?</DialogTitle>
          <DialogDescription>
            Are you sure you want to <span className="font-semibold text-red-600">reset</span> the currently active pay period?
            <br />
            <span className="block mt-2">
              <span className="font-medium">Period Type:</span> {periodType || <span className="italic text-muted-foreground">Unknown</span>}<br />
              <span className="font-medium">Period Name:</span> {periodName || <span className="italic text-muted-foreground">Unnamed</span>}
            </span>
            <span className="block mt-4 text-sm text-muted-foreground">
              This action cannot be undone. All pay period data for this period will be permanently deleted.
            </span>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" onClick={onCancel} className="border-2 border-sky-500">
            Cancel
          </Button>
          <Button variant="destructive" onClick={onReset} autoFocus>
            Reset Pay Periods
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
