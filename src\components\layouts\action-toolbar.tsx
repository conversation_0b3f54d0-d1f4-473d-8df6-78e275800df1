"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useEffect } from "react";
import { useNavigationStore } from "@/store/navigation-store";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Building2,
  Users,
  FileText,
  Calculator,
  CalendarFold,
  FileSpreadsheet,
  Download,
  Printer,
  ChevronRight,
  PinIcon,
  EyeOffIcon,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ActionButton {
  id: string;
  label: string;
  icon: React.ReactNode;
  action?: () => void;
  href?: string;
  iconClassName?: string; // Added for individual icon coloring
}

interface RouteConfig {
  actions: ActionButton[];
  taxYearSelector?: boolean;
}

// Configuration for each section (no leading slash)
import { ResetPayPeriodsDialog } from "@/components/payroll/pay-periods/ResetPayPeriodsDialog";
import { usePayPeriodsQuery, useDeletePayPeriodMutation, useDeletePayPeriodsByTypeAndNameMutation } from "@/hooks/tanstack-query/usePayPeriods";

export function ActionToolbar() {

  const { globalSection, activeEmployerId, employerSections } = useNavigationStore();
  const [taxYear, setTaxYear] = useState("2024/25");
  const [isPeriodSelectorPinned, setIsPeriodSelectorPinned] = useState(true);

  // --- Reset Pay Periods Dialog State ---
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const { data: payPeriods = [] } = usePayPeriodsQuery();
  const deletePayPeriodMutation = useDeletePayPeriodMutation();
  const deletePayPeriodsByTypeAndNameMutation = useDeletePayPeriodsByTypeAndNameMutation();

  // Find the active pay period (assume only one can be active)
  const activePeriod = payPeriods.find(p => p.active);
  const periodType = activePeriod?.type ? activePeriod.type.charAt(0).toUpperCase() + activePeriod.type.slice(1).replace('_', ' ') : null;
  const periodName = activePeriod?.name || null;

  // Handler for reset action
  const handleResetPayPeriods = () => {
    setResetDialogOpen(true);
  };
  const handleConfirmReset = async () => {
    if (activePeriod?.type) {
      await deletePayPeriodsByTypeAndNameMutation.mutateAsync({
        type: activePeriod.type,
        name: activePeriod.name ?? null,
      });
      setResetDialogOpen(false);
    }
  };

  const handleCancelReset = () => setResetDialogOpen(false);

  // Load saved pin state from localStorage on mount
  useEffect(() => {
    const savedPinState = localStorage.getItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
    );
    if (savedPinState !== null) {
      setIsPeriodSelectorPinned(savedPinState === "true");
    }
  }, []);

  // Save pin state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
      isPeriodSelectorPinned.toString(),
    );
  }, [isPeriodSelectorPinned]);

  const togglePeriodSelectorPin = () => {
    setIsPeriodSelectorPinned(!isPeriodSelectorPinned);
  };

  // Handle hover events for the pay periods toggle button
  const handleButtonMouseEnter = () => {
    // When hovering over the button, temporarily show the pay periods selector
    // by setting a temporary localStorage value
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`,
      "true",
    );
  };

  const handleButtonMouseLeave = () => {
    // When leaving the button, remove the temporary visibility flag
    localStorage.removeItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`,
    );
  };

  // Determine current section
  let section = globalSection;
  if (activeEmployerId && employerSections[activeEmployerId]) {
    section = employerSections[activeEmployerId];
  }
  

  const sectionConfigs: Record<string, RouteConfig> = {
    payroll: {
      actions: [
      {
        id: "new-payrun",
        label: "New Payrun",
        icon: <Calculator className="size-6" />,
        iconClassName: "text-emerald-500 dark:text-emerald-400",
      },
      {
        id: "pay-calendar",
        label: "Calendar",
        icon: <CalendarFold className="size-6" />,
        iconClassName: "text-sky-500 dark:text-sky-400",
      },
      {
        id: "reports",
        label: "Reports",
        icon: <FileText className="size-6" />,
        iconClassName: "text-amber-500 dark:text-amber-400",
      },
      {
        id: "reset-pay-periods",
        label: "Reset Pay Periods",
        icon: <EyeOffIcon className="size-6" />,
        iconClassName: "text-red-500 dark:text-red-400",
        action: handleResetPayPeriods,
      },
      {
        id: "month-end",
        label: "Month End",
        icon: <ChevronRight className="size-6" />,
        iconClassName: "text-pink-500 dark:text-pink-400",
      },
    ],
    taxYearSelector: true, // Only show tax year selector on payroll pages
  },
  employees: {
    actions: [
      {
        id: "new-employee",
        label: "New",
        icon: <Users className="size-6" />,
        iconClassName: "text-blue-500 dark:text-blue-400",
      },
      {
        id: "import",
        label: "Import",
        icon: <Download className="size-6" />,
        iconClassName: "text-violet-500 dark:text-violet-400",
      },
      {
        id: "export",
        label: "Export",
        icon: <FileSpreadsheet className="size-6" />,
        iconClassName: "text-emerald-500 dark:text-emerald-400",
      },
      {
        id: "print",
        label: "Print",
        icon: <Printer className="size-6" />,
        iconClassName: "text-slate-500 dark:text-slate-400",
      },
    ],
  },
  employer: {
    actions: [
      {
        id: "new-employer",
        label: "New",
        icon: <Building2 className="size-6" />,
        iconClassName: "text-indigo-500 dark:text-indigo-400",
      },
      {
        id: "import",
        label: "Import",
        icon: <Download className="size-6" />,
        iconClassName: "text-violet-500 dark:text-violet-400",
      },
      {
        id: "export",
        label: "Export",
        icon: <FileSpreadsheet className="size-6" />,
        iconClassName: "text-emerald-500 dark:text-emerald-400",
      },
    ],
  },
  dashboard: { actions: [

  ] },
};
// Get config for section, or fallback to empty actions
const sectionConfig = sectionConfigs[section] || { actions: [] };

const { actions, taxYearSelector } = sectionConfig;

if (!actions.length) return null;


  

  return (
    <div className="w-full items-center pt-2 shadow-slate-500">
      <div className="mt-1 flex h-12 items-center px-4 align-middle">
        {/* Left section - Tax Year Selector */}
        <div className="flex-1">
          {taxYearSelector ? (
            <div className="flex items-center">
              <Select value={taxYear} onValueChange={setTaxYear}>
                <SelectTrigger className="w-[120px] font-semibold text-pink-500">
                  <SelectValue placeholder="Tax Year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2024/25">2024/25</SelectItem>
                  <SelectItem value="2025/26">2025/26</SelectItem>
                </SelectContent>
              </Select>
              <span className="ml-2 text-sm font-medium text-sky-500 italic">
                Tax Year
              </span>

              {/* Pay Periods View Toggle */}
              <div className="ml-6 flex items-center gap-2">
                <Button
                  id="pay-periods-toggle-button"
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-1 rounded-md border px-2 py-1 text-xs font-medium hover:bg-slate-100 dark:hover:bg-zinc-800"
                  onClick={togglePeriodSelectorPin}
                  onMouseEnter={handleButtonMouseEnter}
                  onMouseLeave={handleButtonMouseLeave}
                >
                  {isPeriodSelectorPinned ? (
                    <>
                      <PinIcon className="h-4 w-4 text-sky-500" />
                      <span>Pay periods view</span>
                    </>
                  ) : (
                    <>
                      <EyeOffIcon className="h-4 w-4 text-slate-500" />
                      <span>Pay periods view</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div></div>
          )}
        </div>

        {/* Action buttons in the center */}
        <div className="flex flex-1 items-center justify-center">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className={cn(
                "flex flex-col items-center justify-center px-3 text-sm font-normal transition-all",
                "hover:bg-transparent focus:ring-0 focus:outline-none",
              )}
              onClick={action.action}
            >
              <div
                className={cn(
                  "flex items-center justify-center",
                  action.iconClassName,
                )}
              >
                {action.icon}
              </div>
              <span className="w-full text-center text-xs">{action.label}</span>
            </Button>
          ))}
        </div>

        {/* Right section - Empty div to balance the layout */}
        <div className="flex flex-1 justify-end">
          <div className="w-[120px]"></div>
        </div>
      </div>
      <ResetPayPeriodsDialog
        open={resetDialogOpen}
        periodType={periodType}
        periodName={periodName}
        onOpenChange={setResetDialogOpen}
        onCancel={handleCancelReset}
        onReset={handleConfirmReset}
      />
    </div>
  );
}

