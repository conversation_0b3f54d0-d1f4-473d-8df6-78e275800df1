# Employer DB Integration Plan & Progress (April 2025)

This file tracks the plan and progress for integrating the master and employer databases into the UK Payroll Application UI, as discussed in April 2025. Refer to this file when resuming work on this feature.

---

## Summary of Agreed Plan

### Goal

Replace all mock data in the employer and employee UI sections with real data from the master and employer SQLite databases, using Drizzle ORM and TanStack Query, following all project rules and standards.

### Key Features/Requirements

- Employers list in dashboard tab comes from the master DB
- "Open" employer DB file from the list, file picker, or OS file association
- Add/remove employers to/from the list (stored in master DB)
- All fields in employer and employee sections connect to the employer DB (no mock data)
- Use Drizzle ORM, TanStack Query, Zod validation, and localStorage state as per standards
- Architect for future licensing control (not implemented yet)

---

## Step-by-Step Plan

1. **Audit Current Employers Tab**

   - Identify all mock data usage and entry points for employer DB opening
   - Review existing Add Employer button and UI logic

2. **Replace Mock Data with DB Integration**

   - Create TanStack Query hook to fetch employer list from master DB
   - Remove employerData mock array
   - Handle loading/error states in the UI

3. **Add/Remove Employer Functionality**

   - Add mutation for adding existing employer via file picker
   - Add mutation for removing employer from the list
   - Ensure Add Employer (create new) and Add Existing Employer are clearly distinguished in UI

4. **Employer DB Context/Provider**

   - Implement context/provider to track the currently open employer DB
   - Ensure all child components can access the current employer DB

5. **Connect Employer/Employee UI**

   - Replace mock data in employer and employee sections with DB-backed TanStack Query hooks
   - Ensure all fields are mapped to DB columns and validated with Zod

6. **Testing & Logging**
   - Test all flows (open from list, file picker, OS association)
   - Log progress in this file and update as work continues

---

## Progress Log

- **2025-04-19**: Plan created and agreed with user. Initial audit of employers-tab.tsx completed. Awaiting next session to proceed with DB integration.
- **2025-04-20**: Implemented LibSQL integration for employer databases. Updated file extension constant in src/constants/file.ts.
- **2025-04-21**: Added multi-file selection for adding existing employer databases.
- **2025-04-22**: Implemented employer trading name display in UI and file names while preserving official registered names in the database.
- **2025-04-23**: Added performance optimizations for large employer lists (>500 employers):
  - Implemented file existence caching to reduce file system operations
  - Added threshold-based processing to skip expensive operations for very large lists
  - Improved database connection handling with proper closing in try/finally blocks
- **2025-04-24**: Enhanced UI with loading indicators during refresh operations:
  - Added spinning animation to refresh button when loading
  - Disabled refresh button during loading to prevent multiple clicks
- **2025-04-25**: Fixed UI issues with large employer lists:
  - Made all dialogs scrollable with max-h-[70vh] and overflow-y-auto
  - Added test script to generate 200 test employer databases for performance testing

---

## Next Steps

# Employer DB Integration Progress

## Summary of Achievements (This Session)

- **Crash Prevention & Robust IPC Handling:**
  - Implemented defensive checks and robust error handling in the Electron main process for employer DB opening (including SQLite header validation and clear error reporting for missing/corrupt DBs or tables).
  - Electron no longer crashes when opening invalid or corrupt employer DB files.

- **Tabbed, Multi-Employer UI Architecture:**
  - Confirmed and documented the requirement for a tab-based interface (not new windows) allowing multiple employers to be open at once, each in its own tab.
  - Clicking an employer opens it in a new tab (if not already open) or switches to its tab (if already open). Only one tab is active at a time.
  - Tabs can be closed individually, and closing the active tab activates another or returns to the dashboard if none remain.
  - No open employers or tabs are restored on app launch (dashboard is always the default view).

- **Navigation & State Management:**
  - On opening or switching to an employer tab, the UI navigates to the payroll page for that employer (`/payroll`).
  - State for open employers and active tab is managed in context, but not persisted across app restarts (per user instruction).
  - Persistent state (localStorage) is only used for UI preferences (e.g., column widths, filters), not for restoring open employers/tabs.

- **Service Layer & IPC:**
  - All employer DB operations are performed via a service layer using Electron IPC, keeping the renderer process secure and clean.
  - Logging and error reporting are in place for all IPC interactions.

- **Error Handling:**
  - If opening an employer DB fails, a clear error message is logged and should be surfaced in the UI (not just in the console).

## Next Steps

1. **EmployerDBProvider & Tab UI Implementation**
   - Finalize the EmployerDBProvider context to:
     - Track open employer tabs and the active tab.
     - Provide `openEmployer`, `closeEmployer`, and `switchEmployer` actions.
     - Ensure the UI updates and navigates to `/payroll` for the active employer.
     - Integrate with the EmployersTab component for tab management.

2. **UI/UX Enhancements**
   - Display error messages in the UI when employer DB opening fails.
   - Ensure closing the last employer tab returns the user to the dashboard.
   - Polish the tabbed interface for clarity and usability.

3. **Payroll Page Integration**
   - Ensure the payroll page loads and displays payroll data for the active employer.
   - Use TanStack Query hooks for efficient data fetching and caching.

4. **Employee List & Form Integration**
   - Connect employee lists and forms to the currently open employer DB.
   - Implement create/update/delete with Zod validation.

5. **Testing & Performance**
   - Test all tab, navigation, and DB flows (including with large datasets).
   - Optimize DB queries and tab switching for performance.

6. **Documentation**
   - Update this file and other docs with new features, flows, and performance notes.

---

**When you resume:**
- Pick up with EmployerDBProvider and tabbed navigation integration.
- Refer to project rules and this progress log for standards and requirements.
- Ask for clarification if any behaviour or flow is unclear.
