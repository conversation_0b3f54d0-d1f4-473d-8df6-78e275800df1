"use client";
import { Lato, Open_Sans } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/providers/theme-provider";
import { EmployerDBProvider } from "@/providers/employer-db-provider";
import { MainNavbar } from "@/components/layouts/main-navbar";
import { ActionToolbar } from "@/components/layouts/action-toolbar";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";

// Load Lato font (primary)
const lato = Lato({
  subsets: ["latin"],
  weight: ["300", "400", "700", "900"],
  variable: "--font-lato",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Create the client only once per app instance
  const [queryClient] = useState(() => new QueryClient());

  return (
    <html lang="en" suppressHydrationWarning className={`${lato.variable}`}>
      <QueryClientProvider client={queryClient}>
        <EmployerDBProvider>
          <ThemeProvider defaultTheme="light">
            <body className="bg-background flex h-screen min-h-0 flex-col overflow-hidden px-2 font-sans subpixel-antialiased">
              <MainNavbar />
              <ActionToolbar />
              <main className="flex min-h-0 flex-1 flex-col">{children}</main>
            </body>
          </ThemeProvider>
        </EmployerDBProvider>
      </QueryClientProvider>
    </html>
  );
}
