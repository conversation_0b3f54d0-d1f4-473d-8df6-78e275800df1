# Payroll Section Database Wiring & Pay Period Setup Plan

## Overview
This document outlines the step-by-step plan for wiring up the Payroll section to the database, replacing all mock data, and implementing a robust, flexible pay period setup. It follows the project’s core architecture, database, and UI state management rules.

**Scope:**
- Remove all mock/dummy payroll data
- Connect Payroll UI (overview, batch editor, payslip editor) to the live database
- Implement pay period setup and management per employer
- Ensure persistent, per-employer UI state using Zustand with persist middleware, storing data in localStorage and namespaced per employer, using the prefix from `EMPLOYER_DB_LOCALSTORAGE_PREFIX` in `src/constants/file.ts`
- Enable live data sync across all payroll views
- Calculation logic integration is out of scope for this phase but will be added immediately after this phase

---

## 1. Remove Mock Data
- Identify all locations in the Payroll section (UI/components/hooks) that use mock or hard-coded data.
- Replace with TanStack Query hooks fetching real data from the employer’s payroll database.
- Ensure all test/dummy data sources are removed from codebase.
- Update tests/mocks to use real DB-backed data where possible.

## 2. Database Schema Updates
- **Pay Periods Table:**
  - Add a `pay_periods` table to each employer database:
    - `id` (PK, UUID)
    - `name` (nullable, for custom naming)
    - `type` (enum: weekly, two_weekly, four_weekly, monthly, quarterly, yearly)
    - `start_day` (int, e.g. 1–31 or 0–6 for day-of-week)
    - `end_day` (int)
    - `pay_date_rule` (JSON: options for on/after/before, N days, fixed date, etc.)
    - `active` (boolean)
    - `created_at`, `updated_at`
    - `notes` (nullable)
  - Add necessary indexes for performance.
- **Employee Link:**
  - Update employees table to reference assigned pay period (`pay_period_id` FK, nullable for unassigned).
  - Ensure employee start date is present and validated.
- **Payslips Table:**
  - Ensure payslips reference both employee and pay period.
  - Add `status` (open, closed, reopened, etc.) and `finalised_at` timestamp.

## 3. Pay Period Setup Wizard
- Implement a wizard flow for pay period configuration:
  - Step 1: Select pay period types (multiple allowed)
  - Step 2: Configure each period (custom name, start/end days, pay date rule)
  - Step 3: Assign employees to pay periods (with validation, no overlaps)
  - Step 4: Generate all pay periods for the current tax year (using employee start dates, allow skipping historical periods)
- Store wizard progress in Zustand (with persist) and localStorage, using the prefix from `EMPLOYER_DB_LOCALSTORAGE_PREFIX` in `src/constants/file.ts`, keyed per employer.
- On completion, persist configuration to employer DB and trigger pay period generation.

## 4. Pay Period Generation Logic
- For each configured pay period:
  - Generate all periods for the tax year (using custom start/end/pay date rules)
  - For new/switching employers, allow user to select starting point (beginning of year, specific date, etc.)
  - Ensure no overlapping periods for any employee
  - Mark historical periods as skipped if not needed, require zero payslips if skipped for compliance
- Store generated periods in `pay_periods` and related tables.

## 5. Payroll UI Wiring
- **Overview Table, Batch Editor, Payslip Editor:**
  - Replace all mock data with TanStack Query hooks fetching from DB
  - Use custom query keys per employer and pay period for caching
  - Implement optimistic updates and live sync across all views
  - Ensure all views reflect changes immediately (editing in one view updates others)
- **Persistent State:**
  - Store UI state (view, filters, columns, sorting, pagination, selected items) in Zustand with the persist middleware, which stores data in localStorage. Use the `EMPLOYER_DB_LOCALSTORAGE_PREFIX` constant from `src/constants/file.ts` for the prefix, and namespace per employer.
  - Initialise state from persisted storage or defaults, debounce saves, handle errors gracefully

## 6. Pay Run Finalisation & Reopening
- When a pay run is closed:
  - Lock all payslip fields for that period
  - Automatically trigger RTI and pension submissions
- When reopening:
  - Allow reopening one, multiple, or all payslips for a period
  - Present options for resetting downstream payslips (reset, fast-forward, or keep items)
  - Do not auto-trigger new FPS/pension submissions on re-finalisation
- Ensure all actions are logged and auditable

## 7. Testing & Validation
- Add/expand tests to cover:
  - Pay period creation, assignment, and validation (no overlaps)
  - UI state persistence and restoration
  - Live sync between all payroll views
  - Finalisation/reopening workflows
- Manual QA: walk through wizard, pay run, and editing flows for multiple employers and period types

## 8. Documentation & Progress Tracking
- Update architecture and technical docs as needed
- Log progress in `@/progress-log.md` at each major milestone
- Record any significant schema or UI changes in `@/change-log.md`

---

## 9. Use Centralised Constants for File Extension and Storage Prefix
- **Never hardcode the `.ukpayroll` extension or any related string.**
- Always use the constants from `src/constants/file.ts`:
  - `EMPLOYER_DB_EXTENSION` for the file extension
  - `EMPLOYER_DB_LOCALSTORAGE_PREFIX` for localStorage prefixing
- Update all implementation and documentation to reference these constants. This ensures easy rebranding or extension changes in the future.

---

**Next Steps:**
1. Implement DB schema changes and migrations
2. Build pay period setup wizard UI and logic
3. Wire up payroll tables and editors to DB
4. Implement pay run finalisation/reopening logic
5. Test and validate all flows

*Calculation logic integration will follow in a subsequent phase.*

---

**References:**
- [Development Plan](./development-plan.md)
- [AI Coding Guidelines](./ai-coding-guidelines-FULL.md)
- [Technical Implementation Guide](./Technical Implementation Guide/)
- [Project Memories]

*This plan adheres to all core architecture, database, and UI state management rules for the UK Payroll application.*
