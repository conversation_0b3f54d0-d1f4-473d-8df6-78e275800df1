<PERSON>@Workstation-14900K MINGW64 /a/WEBSITES/Payroll_software-V2/uk-payroll (main)
$ rm -rf node_modules
rm -rf package-lock.j<PERSON>@Workstation-14900K MINGW64 /a/WEBSITES/Payroll_software-V2/uk-payroll (main)
$ npm install -g node-gyp@9.4.1
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated @npmcli/move-file@2.0.1: This functionality has been moved to @npmcli/fs
npm warn deprecated npmlog@6.0.2: This package is no longer supported.
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@8.1.0: Glob versions prior to v9 are no longer supported
npm warn deprecated are-we-there-yet@3.0.1: This package is no longer supported.
npm warn deprecated gauge@4.0.4: This package is no longer supported.

added 37 packages, removed 41 packages, and changed 59 packages in 1s

5 packages are looking for funding
  run `npm fund` for details

Chris@Workstation-14900K MINGW64 /a/WEBSITES/Payroll_software-V2/uk-payroll (main)
$ npm install --save-exact @electron/rebuild@3.6.0
npm warn skipping integrity check for git dependency ssh://**************/electron/node-gyp.git
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated @npmcli/move-file@2.0.1: This functionality has been moved to @npmcli/fs
npm warn deprecated @esbuild-kit/esm-loader@2.6.5: Merged into tsx: https://tsx.is
npm warn deprecated npmlog@5.0.1: This package is no longer supported.
npm warn deprecated npmlog@6.0.2: This package is no longer supported.
npm warn deprecated rimraf@2.6.3: Rimraf versions prior to v4 are no longer supported
npm warn deprecated @esbuild-kit/core-utils@3.3.2: Merged into tsx: https://tsx.is
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated glob@8.1.0: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@8.1.0: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated are-we-there-yet@3.0.1: This package is no longer supported.
npm warn deprecated are-we-there-yet@2.0.0: This package is no longer supported.
npm warn deprecated boolean@3.2.0: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
npm warn deprecated gauge@4.0.4: This package is no longer supported.
npm warn deprecated gauge@3.0.2: This package is no longer supported.
npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
npm warn cleanup Failed to remove some directories [
npm warn cleanup   [
npm warn cleanup     'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules',
npm warn cleanup     [Error: EPERM: operation not permitted, rmdir 'A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\next\dist\server\app-render'] {
npm warn cleanup       errno: -4048,
npm warn cleanup       code: 'EPERM',
npm warn cleanup       syscall: 'rmdir',
npm warn cleanup       path: 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\next\\dist\\server\\app-render'
npm warn cleanup     }
npm warn cleanup   ],
npm warn cleanup   [
npm warn cleanup     'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\next',
npm warn cleanup     [Error: EPERM: operation not permitted, rmdir 'A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\next\dist\compiled\@babel'] {
npm warn cleanup       errno: -4048,
npm warn cleanup       code: 'EPERM',
npm warn cleanup       syscall: 'rmdir',
npm warn cleanup       path: 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\next\\dist\\compiled\\@babel'
npm warn cleanup     }
npm warn cleanup   ]
npm warn cleanup ]
npm error code 1
npm error path A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3
npm error command failed
npm error command C:\Windows\system32\cmd.exe /d /s /c prebuild-install || node-gyp rebuild --release
npm error Build started 20/04/2025 10:28:25.
npm error
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\binding.sln" on node 1 (default targets).
npm error ValidateSolutionConfiguration:
npm error   Building solution configuration "Release|x64".
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\binding.sln" (1) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj.metaproj" (2) on node 1 (default targets).
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj.metaproj" (2) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\sqlite3.vcxproj.metaproj" (3) on node 1 (default targets).
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\sqlite3.vcxproj.metaproj" (3) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\locate_sqlite3.vcxproj" (4) on node 1 (default targets).
npm error PrepareForBuild:
npm error   Creating directory "Release\obj\locate_sqlite3\".
npm error   Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
npm error   Creating directory "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\".
npm error   Creating directory "Release\obj\locate_sqlite3\locate_sqlite3.tlog\".
npm error InitializeBuildStatus:
npm error   Creating "Release\obj\locate_sqlite3\locate_sqlite3.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
npm error   Touching "Release\obj\locate_sqlite3\locate_sqlite3.tlog\unsuccessfulbuild".
npm error ComputeCustomBuildOutput:
npm error   Creating directory "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\global_intermediate\sqlite3\".
npm error CustomBuild:
npm error   copy_builtin_sqlite3
npm error FinalizeBuildStatus:
npm error   Deleting file "Release\obj\locate_sqlite3\locate_sqlite3.tlog\unsuccessfulbuild".
npm error   Touching "Release\obj\locate_sqlite3\locate_sqlite3.tlog\locate_sqlite3.lastbuildstate".
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\locate_sqlite3.vcxproj" (default targets).
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\sqlite3.vcxproj.metaproj" (3) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\sqlite3.vcxproj" (5) on node 1 (default targets).
npm error PrepareForBuild:
npm error   Creating directory "Release\obj\sqlite3\".
npm error   Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
npm error   Creating directory "Release\obj\sqlite3\sqlite3.tlog\".
npm error InitializeBuildStatus:
npm error   Creating "Release\obj\sqlite3\sqlite3.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
npm error   Touching "Release\obj\sqlite3\sqlite3.tlog\unsuccessfulbuild".
npm error MakeDirsForCl:
npm error   Creating directory "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\Release\obj\sqlite3\obj\global_intermediate\sqlite3".
npm error ClCompile:
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\src" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\config" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\openssl\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\uv\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\zlib" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\v8\include" /I"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\global_intermediate\sqlite3" /Z7 /nologo /W3 /WX- /diagnostics:column /MP /Ox /Ob2 /Oi /Ot /Oy /GL /D NODE_GYP_MODULE_NAME=sqlite3 /D USING_UV_SHARED=1 /D USING_V8_SHARED=1 /D V8_DEPRECATION_WARNINGS=1 /D _GLIBCXX_USE_CXX11_ABI=1 /D ELECTRON_ENSURE_CONFIG_GYPI /D WIN32 /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _HAS_EXCEPTIONS=0 /D NOMINMAX /D OPENSSL_NO_PINSHARED /D OPENSSL_THREADS /D HAVE_INT16_T=1 /D HAVE_INT32_T=1 /D HAVE_INT8_T=1 /D HAVE_STDINT_H=1 /D HAVE_UINT16_T=1 /D HAVE_UINT32_T=1 /D HAVE_UINT8_T=1 /D "SQLITE_DEFAULT_CACHE_SIZE=-16000" /D SQLITE_DEFAULT_FOREIGN_KEYS=1 /D SQLITE_DEFAULT_MEMSTATUS=0 /D SQLITE_DEFAULT_WAL_SYNCHRONOUS=1 /D SQLITE_DQS=0 /D SQLITE_ENABLE_COLUMN_METADATA /D SQLITE_ENABLE_DESERIALIZE /D SQLITE_ENABLE_FTS3 /D SQLITE_ENABLE_FTS3_PARENTHESIS /D SQLITE_ENABLE_FTS4 /D SQLITE_ENABLE_FTS5 /D SQLITE_ENABLE_GEOPOLY /D SQLITE_ENABLE_JSON1 /D SQLITE_ENABLE_MATH_FUNCTIONS /D SQLITE_ENABLE_RTREE /D SQLITE_ENABLE_STAT4 /D SQLITE_ENABLE_UPDATE_DELETE_LIMIT /D SQLITE_INTROSPECTION_PRAGMAS /D SQLITE_LIKE_DOESNT_MATCH_BLOBS /D SQLITE_OMIT_DEPRECATED /D SQLITE_OMIT_GET_TABLE /D SQLITE_OMIT_PROGRESS_CALLBACK /D SQLITE_OMIT_SHARED_CACHE /D SQLITE_OMIT_TCL_VARIABLE /D SQLITE_SOUNDEX /D SQLITE_THREADSAFE=2 /D SQLITE_TRACE_SIZE_LIMIT=32 /D SQLITE_USE_URI=0 /D "HOST_BINARY=\"node.exe\"" /D NDEBUG /GF /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR- /Fo"Release\obj\sqlite3\\obj\global_intermediate\sqlite3\sqlite3.obj" /Fd"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\sqlite3.pdb" /external:W3 /Gd /TC /wd4351 /wd4355 /wd4800 /wd4251 /wd4275 /wd4244 /wd4267 /FC /errorReport:queue /Zc:__cplusplus -std:c++20 /Zm2000 "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\\obj\global_intermediate\sqlite3\sqlite3.c"
npm error   sqlite3.c
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\src" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\config" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\openssl\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\uv\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\zlib" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\v8\include" /I"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\global_intermediate\sqlite3" /Z7 /nologo /W3 /WX- /diagnostics:column /MP /Ox /Ob2 /Oi /Ot /Oy /GL /D NODE_GYP_MODULE_NAME=sqlite3 /D USING_UV_SHARED=1 /D USING_V8_SHARED=1 /D V8_DEPRECATION_WARNINGS=1 /D _GLIBCXX_USE_CXX11_ABI=1 /D ELECTRON_ENSURE_CONFIG_GYPI /D WIN32 /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _HAS_EXCEPTIONS=0 /D NOMINMAX /D OPENSSL_NO_PINSHARED /D OPENSSL_THREADS /D HAVE_INT16_T=1 /D HAVE_INT32_T=1 /D HAVE_INT8_T=1 /D HAVE_STDINT_H=1 /D HAVE_UINT16_T=1 /D HAVE_UINT32_T=1 /D HAVE_UINT8_T=1 /D "SQLITE_DEFAULT_CACHE_SIZE=-16000" /D SQLITE_DEFAULT_FOREIGN_KEYS=1 /D SQLITE_DEFAULT_MEMSTATUS=0 /D SQLITE_DEFAULT_WAL_SYNCHRONOUS=1 /D SQLITE_DQS=0 /D SQLITE_ENABLE_COLUMN_METADATA /D SQLITE_ENABLE_DESERIALIZE /D SQLITE_ENABLE_FTS3 /D SQLITE_ENABLE_FTS3_PARENTHESIS /D SQLITE_ENABLE_FTS4 /D SQLITE_ENABLE_FTS5 /D SQLITE_ENABLE_GEOPOLY /D SQLITE_ENABLE_JSON1 /D SQLITE_ENABLE_MATH_FUNCTIONS /D SQLITE_ENABLE_RTREE /D SQLITE_ENABLE_STAT4 /D SQLITE_ENABLE_UPDATE_DELETE_LIMIT /D SQLITE_INTROSPECTION_PRAGMAS /D SQLITE_LIKE_DOESNT_MATCH_BLOBS /D SQLITE_OMIT_DEPRECATED /D SQLITE_OMIT_GET_TABLE /D SQLITE_OMIT_PROGRESS_CALLBACK /D SQLITE_OMIT_SHARED_CACHE /D SQLITE_OMIT_TCL_VARIABLE /D SQLITE_SOUNDEX /D SQLITE_THREADSAFE=2 /D SQLITE_TRACE_SIZE_LIMIT=32 /D SQLITE_USE_URI=0 /D "HOST_BINARY=\"node.exe\"" /D NDEBUG /GF /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR- /Fo"Release\obj\sqlite3\\" /Fd"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\sqlite3.pdb" /external:W3 /Gd /TP /wd4351 /wd4355 /wd4800 /wd4251 /wd4275 /wd4244 /wd4267 /FC /errorReport:queue /Zc:__cplusplus -std:c++20 /Zm2000 "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc"
npm error   win_delay_load_hook.cc
npm error Lib:
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\Lib.exe /OUT:"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\sqlite3.lib" /NOLOGO /MACHINE:X64 /LTCG:INCREMENTAL Release\obj\sqlite3\win_delay_load_hook.obj
npm error   Release\obj\sqlite3\\obj\global_intermediate\sqlite3\sqlite3.obj
npm error   sqlite3.vcxproj -> A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\\sqlite3.lib
npm error FinalizeBuildStatus:
npm error   Deleting file "Release\obj\sqlite3\sqlite3.tlog\unsuccessfulbuild".
npm error   Touching "Release\obj\sqlite3\sqlite3.tlog\sqlite3.lastbuildstate".
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\sqlite3.vcxproj" (default targets). 
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\deps\sqlite3.vcxproj.metaproj" (default targets).
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj.metaproj" (2) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj" (6) on node 1 (default targets).
npm error PrepareForBuild:
npm error   Creating directory "Release\obj\better_sqlite3\".
npm error   Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
npm error   Creating directory "Release\obj\better_sqlite3\better_sqlite3.tlog\".
npm error InitializeBuildStatus:
npm error   Creating "Release\obj\better_sqlite3\better_sqlite3.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
npm error   Touching "Release\obj\better_sqlite3\better_sqlite3.tlog\unsuccessfulbuild".
npm error MakeDirsForCl:
npm error   Creating directory "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\better_sqlite3\src".
npm error ClCompile:
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\src" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\config" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\openssl\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\uv\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\zlib" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\v8\include" /I"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\global_intermediate\sqlite3" /Z7 /nologo /W3 /WX- /diagnostics:column /MP /Ox /Ob2 /Oi /Ot /Oy /GL /D NODE_GYP_MODULE_NAME=better_sqlite3 /D USING_UV_SHARED=1 /D USING_V8_SHARED=1 /D V8_DEPRECATION_WARNINGS=1 /D _GLIBCXX_USE_CXX11_ABI=1 /D ELECTRON_ENSURE_CONFIG_GYPI /D WIN32 /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _HAS_EXCEPTIONS=0 /D NOMINMAX /D OPENSSL_NO_PINSHARED /D OPENSSL_THREADS /D BUILDING_NODE_EXTENSION /D "HOST_BINARY=\"node.exe\"" /D NDEBUG /D _WINDLL /GF /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR- /Fo"Release\obj\better_sqlite3\\src\better_sqlite3.obj" /Fd"Release\obj\better_sqlite3\vc143.pdb" /external:W3 /Gd /TP /wd4351 /wd4355 /wd4800 /wd4251 /wd4275 /wd4244 /wd4267 /FC /errorReport:queue /Zc:__cplusplus -std:c++20 /Zm2000 /std:c++17 ..\src\better_sqlite3.cpp
npm error cl : command line  warning D9025: overriding '/std:c++20' with '/std:c++17' [A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj]
npm error   better_sqlite3.cpp
npm error C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node\node.h(27,1): error C1189: #error:  "It looks like you are building this native module without using the right config.gypi.  This normally means that you need to update electron-rebuild (>=3.2.8) or node-gyp (>=9.0.0) if you're building modules directly." [A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj]
npm error   (compiling source file '../src/better_sqlite3.cpp')
npm error
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj" (default targets) -- FAILED.
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj.metaproj" (default targets) -- FAILED.
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\binding.sln" (1) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\test_extension.vcxproj.metaproj" (7) on node 1 (default targets).
npm error Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\test_extension.vcxproj.metaproj" (7) is building "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\test_extension.vcxproj" (8) on node 1 (default targets).
npm error PrepareForBuild:
npm error   Creating directory "Release\obj\test_extension\".
npm error   Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
npm error   Creating directory "Release\obj\test_extension\test_extension.tlog\".
npm error InitializeBuildStatus:
npm error   Creating "Release\obj\test_extension\test_extension.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
npm error   Touching "Release\obj\test_extension\test_extension.tlog\unsuccessfulbuild".
npm error MakeDirsForCl:
npm error   Creating directory "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\test_extension\deps".
npm error ClCompile:
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\src" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\config" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\openssl\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\uv\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\zlib" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\v8\include" /I"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\global_intermediate\sqlite3" /Z7 /nologo /W3 /WX- /diagnostics:column /MP /Ox /Ob2 /Oi /Ot /Oy /GL /D NODE_GYP_MODULE_NAME=test_extension /D USING_UV_SHARED=1 /D USING_V8_SHARED=1 /D V8_DEPRECATION_WARNINGS=1 /D _GLIBCXX_USE_CXX11_ABI=1 /D ELECTRON_ENSURE_CONFIG_GYPI /D WIN32 /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _HAS_EXCEPTIONS=0 /D NOMINMAX /D OPENSSL_NO_PINSHARED /D OPENSSL_THREADS /D BUILDING_NODE_EXTENSION /D "HOST_BINARY=\"node.exe\"" /D NDEBUG /D _WINDLL /GF /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR- /Fo"Release\obj\test_extension\\deps\test_extension.obj" /Fd"Release\obj\test_extension\vc143.pdb" /external:W3 /Gd /TC /wd4351 /wd4355 /wd4800 /wd4251 /wd4275 /wd4244 /wd4267 /FC /errorReport:queue /Zc:__cplusplus -std:c++20 /Zm2000 ..\deps\test_extension.c
npm error   test_extension.c
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\src" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\config" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\openssl\openssl\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\uv\include" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\zlib" /I"C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\deps\v8\include" /I"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\obj\global_intermediate\sqlite3" /Z7 /nologo /W3 /WX- /diagnostics:column /MP /Ox /Ob2 /Oi /Ot /Oy /GL /D NODE_GYP_MODULE_NAME=test_extension /D USING_UV_SHARED=1 /D USING_V8_SHARED=1 /D V8_DEPRECATION_WARNINGS=1 /D _GLIBCXX_USE_CXX11_ABI=1 /D ELECTRON_ENSURE_CONFIG_GYPI /D WIN32 /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _HAS_EXCEPTIONS=0 /D NOMINMAX /D OPENSSL_NO_PINSHARED /D OPENSSL_THREADS /D BUILDING_NODE_EXTENSION /D "HOST_BINARY=\"node.exe\"" /D NDEBUG /D _WINDLL /GF /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR- /Fo"Release\obj\test_extension\\" /Fd"Release\obj\test_extension\vc143.pdb" /external:W3 /Gd /TP /wd4351 /wd4355 /wd4800 /wd4251 /wd4275 /wd4244 /wd4267 /FC /errorReport:queue /Zc:__cplusplus -std:c++20 /Zm2000 "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc"
npm error   win_delay_load_hook.cc
npm error Link:
npm error   C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\test_extension.node" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib DelayImp.lib "C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\35.1.2\\x64\\node.lib" Delayimp.lib /DELAYLOAD:node.exe /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\test_extension.pdb" /OPT:REF /OPT:ICF /TLBID:1 /DYNAMICBASE /NXCOMPAT /MACHINE:X64 /LTCG:INCREMENTAL /ignore:4199 /DLL Release\obj\test_extension\win_delay_load_hook.obj
npm error   Release\obj\test_extension\\deps\test_extension.obj
npm error   "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\sqlite3.lib"
npm error      Creating library A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\test_extension.lib and object A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\test_extension.exp
npm error   Generating code
npm error   Previous IPDB not found, fall back to full compilation.
npm error   All 3 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
npm error   Finished generating code
npm error   test_extension.vcxproj -> A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\Release\\test_extension.node        
npm error FinalizeBuildStatus:
npm error   Deleting file "Release\obj\test_extension\test_extension.tlog\unsuccessfulbuild".
npm error   Touching "Release\obj\test_extension\test_extension.tlog\test_extension.lastbuildstate".
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\test_extension.vcxproj" (default targets).
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\test_extension.vcxproj.metaproj" (default targets).
npm error Done Building Project "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\binding.sln" (default targets) -- FAILED.
npm error
npm error Build FAILED.
npm error
npm error "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\binding.sln" (default target) (1) ->
npm error "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj.metaproj" (default target) (2) ->       
npm error "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj" (default target) (6) ->
npm error (ClCompile target) ->
npm error   cl : command line  warning D9025: overriding '/std:c++20' with '/std:c++17' [A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj]
npm error
npm error
npm error "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\binding.sln" (default target) (1) ->
npm error "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj.metaproj" (default target) (2) ->       
npm error "A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj" (default target) (6) ->
npm error (ClCompile target) ->
npm error   C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2\include\node\node.h(27,1): error C1189: #error:  "It looks like you are building this native module without using the right config.gypi.  This normally means that you need to update electron-rebuild (>=3.2.8) or node-gyp (>=9.0.0) if you're building modules directly." [A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\better_sqlite3.vcxproj]
npm error
npm error     1 Warning(s)
npm error     1 Error(s)
npm error
npm error Time Elapsed 00:00:01.81
npm error gyp info it worked if it ends with ok
npm error gyp verb cli [
npm error gyp verb cli 'C:\\Program Files\\nodejs\\node.exe',
npm error gyp verb cli 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\bin\\node-gyp.js',
npm error gyp verb cli 'rebuild',
npm error gyp verb cli '--release'
npm error gyp verb cli ]
npm error gyp info using node-gyp@10.2.0-electron.1
npm error gyp info using node@20.19.0 | win32 | x64
npm error gyp verb clean removing "build" directory
npm error gyp verb find Python checking Python explicitly set from command line or npm configuration
npm error gyp verb find Python - "--python=" or "npm config get python" is "C:\Program Files\Python311\python.exe"
npm error gyp verb find Python - executing "C:\Program Files\Python311\python.exe" to get executable path
npm error gyp sill find Python execFile: exec = "\"C:\\Program Files\\Python311\\python.exe\""
npm error gyp sill find Python execFile: args = ["\"-c\"","\"import sys; sys.stdout.buffer.write(sys.executable.encode('utf-8'));\""]
npm error gyp sill find Python execFile: opts = {"env":{"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","BUNDLED_DEBUGPY_PATH":"c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\libs\\debugpy","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133895900671599873","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_16144_KTWRETPXFFGHLRQY","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"WORKSTATION-149","COMSPEC":"C:\\Windows\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\Windows\\notepad.exe","EFC_9824":"1","EXEPATH":"C:\\Program Files\\Git\\bin","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","HOME":"C:\\Users\\<USER>\\Users\\Chris","HOSTNAME":"Workstation-14900K","IGCCSVC_DB":"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAtu7b3Q6oNkyHKP52rq/BFgQAAAACAAAAAAAQZgAAAAEAACAAAAALAwF36SphtnzBh2f2KU7lUREn03F1uo0ViG/mVWzblAAAAAAOgAAAAAIAACAAAADjUGRkNxLz2LNxY1092QCyUeQepBCFc4gds4b8cssiemAAAACu68cd/vsYxgUsq/by4jkPcNF4PqnOxaEMvMEFySyLIBRGioTIgeyKYkcyJXoUM5+31eSOCAa82JWKZuaJYBwMJwXgwG93WK7YR5lICDMLr6EgQQItL6KLuE7WvPoNb55AAAAARv4dNJQ7j5KmaAHQJ15zn7V/oii9SsRvvvRsZ11dDJf2Init8DcFuFteZcDbR2UzQshWVRJg/sAnNS+RSpvNsA==","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\WORKSTATION-149","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","npm_command":"install","npm_config_better_sqlite3_binary_host_mirror":"https://github.com/WiseLibs/better-sqlite3/releases/download","npm_config_build_from_source":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_electron_mirror":"https://github.com/electron/electron/releases/download/v","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_local_prefix":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","npm_config_msvs_version":"2022","npm_config_node_gyp":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_noproxy":"","npm_config_npm_version":"11.0.0","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_python":"C:\\Program Files\\Python311\\python.exe","npm_config_runtime":"electron","npm_config_save_exact":"true","npm_config_target":"35.1.2","npm_config_target_arch":"x64","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/11.0.0 node/v20.19.0 win32 x64 workspaces/false","npm_execpath":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"install","npm_lifecycle_script":"prebuild-install || node-gyp rebuild --release","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_dev":"","npm_package_dev_optional":"","npm_package_integrity":"sha512-JhTZjpyapA1icCEjIZB4TSSgkGdFgpWZA2Wszg7Cf4JwJwKQmbvuNnJBeR+EYG/Z29OXvR4G//Rbg31BW/Z7Yg==","npm_package_json":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\package.json","npm_package_name":"better-sqlite3","npm_package_optional":"","npm_package_peer":"","npm_package_resolved":"https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.0.1.tgz","npm_package_version":"8.0.1","NUMBER_OF_PROCESSORS":"32","NVAR_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA AR SDK\\models","NVM_HOME":"C:\\Users\\<USER>\\AppData\\Local\\nvm","NVM_SYMLINK":"C:\\nvm4w\\nodejs","NVVFX_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA Video Effects\\models","OneDrive":"A:\\OneDrive - Lydian Ledgers Ltd","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PATH":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\node_modules\\.bin;A:\\WEBSITES\\node_modules\\.bin;A:\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 183 Stepping 1, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"b701","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\Windows","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"dumb","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.99.3","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"WORKSTATION-149","USERDOMAIN_ROAMINGPROFILE":"WORKSTATION-149","USERNAME":"Chris","USERPROFILE":"C:\\Users\\<USER>\\Users\\Chris\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-88ddf9b0884b2b39.txt","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-65bebf4f32-sock","WINDIR":"C:\\Windows","ZES_ENABLE_SYSMAN":"1","_":"C:/Program Files/nodejs/node.exe"},"shell":true}
npm error gyp sill find Python execFile result: err = "Error: Command failed: \"C:\\Program Files\\Python311\\python.exe\" \"-c\" \"import sys; sys.stdout.buffer.write(sys.executable.encode('utf-8'));\"\nThe system cannot find the path specified.\r\n\n    at genericNodeError (node:internal/errors:984:15)\n    at wrappedFn (node:internal/errors:538:14)\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:524:28)\n    at maybeClose (node:internal/child_process:1104:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)"
npm error gyp sill find Python execFile result: stdout = ""
npm error gyp sill find Python execFile result: stderr = "The system cannot find the path specified.\r\n"
npm error gyp verb find Python - executable path is ""
npm error gyp verb find Python - executing "" to get version
npm error gyp sill find Python execFile: exec = ""
npm error gyp sill find Python execFile: args = ["-c","import sys; print(\"%s.%s.%s\" % sys.version_info[:3]);"]
npm error gyp sill find Python execFile: opts = {"env":{"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","BUNDLED_DEBUGPY_PATH":"c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\libs\\debugpy","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133895900671599873","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_16144_KTWRETPXFFGHLRQY","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"WORKSTATION-149","COMSPEC":"C:\\Windows\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\Windows\\notepad.exe","EFC_9824":"1","EXEPATH":"C:\\Program Files\\Git\\bin","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","HOME":"C:\\Users\\<USER>\\Users\\Chris","HOSTNAME":"Workstation-14900K","IGCCSVC_DB":"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAtu7b3Q6oNkyHKP52rq/BFgQAAAACAAAAAAAQZgAAAAEAACAAAAALAwF36SphtnzBh2f2KU7lUREn03F1uo0ViG/mVWzblAAAAAAOgAAAAAIAACAAAADjUGRkNxLz2LNxY1092QCyUeQepBCFc4gds4b8cssiemAAAACu68cd/vsYxgUsq/by4jkPcNF4PqnOxaEMvMEFySyLIBRGioTIgeyKYkcyJXoUM5+31eSOCAa82JWKZuaJYBwMJwXgwG93WK7YR5lICDMLr6EgQQItL6KLuE7WvPoNb55AAAAARv4dNJQ7j5KmaAHQJ15zn7V/oii9SsRvvvRsZ11dDJf2Init8DcFuFteZcDbR2UzQshWVRJg/sAnNS+RSpvNsA==","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\WORKSTATION-149","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","npm_command":"install","npm_config_better_sqlite3_binary_host_mirror":"https://github.com/WiseLibs/better-sqlite3/releases/download","npm_config_build_from_source":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_electron_mirror":"https://github.com/electron/electron/releases/download/v","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_local_prefix":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","npm_config_msvs_version":"2022","npm_config_node_gyp":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_noproxy":"","npm_config_npm_version":"11.0.0","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_python":"C:\\Program Files\\Python311\\python.exe","npm_config_runtime":"electron","npm_config_save_exact":"true","npm_config_target":"35.1.2","npm_config_target_arch":"x64","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/11.0.0 node/v20.19.0 win32 x64 workspaces/false","npm_execpath":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"install","npm_lifecycle_script":"prebuild-install || node-gyp rebuild --release","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_dev":"","npm_package_dev_optional":"","npm_package_integrity":"sha512-JhTZjpyapA1icCEjIZB4TSSgkGdFgpWZA2Wszg7Cf4JwJwKQmbvuNnJBeR+EYG/Z29OXvR4G//Rbg31BW/Z7Yg==","npm_package_json":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\package.json","npm_package_name":"better-sqlite3","npm_package_optional":"","npm_package_peer":"","npm_package_resolved":"https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.0.1.tgz","npm_package_version":"8.0.1","NUMBER_OF_PROCESSORS":"32","NVAR_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA AR SDK\\models","NVM_HOME":"C:\\Users\\<USER>\\AppData\\Local\\nvm","NVM_SYMLINK":"C:\\nvm4w\\nodejs","NVVFX_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA Video Effects\\models","OneDrive":"A:\\OneDrive - Lydian Ledgers Ltd","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PATH":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\node_modules\\.bin;A:\\WEBSITES\\node_modules\\.bin;A:\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 183 Stepping 1, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"b701","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\Windows","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"dumb","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.99.3","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"WORKSTATION-149","USERDOMAIN_ROAMINGPROFILE":"WORKSTATION-149","USERNAME":"Chris","USERPROFILE":"C:\\Users\\<USER>\\Users\\Chris\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-88ddf9b0884b2b39.txt","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-65bebf4f32-sock","WINDIR":"C:\\Windows","ZES_ENABLE_SYSMAN":"1","_":"C:/Program Files/nodejs/node.exe"},"shell":false}
npm error gyp sill find Python execFile: threw:
npm error gyp sill find Python TypeError [ERR_INVALID_ARG_VALUE]: The argument 'file' cannot be empty. Received ''
npm error gyp sill find Python at normalizeSpawnArguments (node:child_process:552:11)
npm error gyp sill find Python at spawn (node:child_process:755:13)
npm error gyp sill find Python at Object.execFile (node:child_process:351:17)
npm error gyp sill find Python at A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\util.js:9:20
npm error gyp sill find Python at new Promise (<anonymous>)
npm error gyp sill find Python at PythonFinder.execFile (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\util.js:8:37)  
npm error gyp sill find Python at PythonFinder.run (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\find-python.js:265:48)
npm error gyp sill find Python at PythonFinder.checkExecPath (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\find-python.js:230:34)
npm error gyp sill find Python at PythonFinder.checkCommand (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\find-python.js:189:19)
npm error gyp sill find Python at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
npm error gyp verb find Python - "" could not be run
npm error gyp sill find Python runChecks: err = "TypeError [ERR_INVALID_ARG_VALUE]: The argument 'file' cannot be empty. Received ''\n    at normalizeSpawnArguments (node:child_process:552:11)\n    at spawn (node:child_process:755:13)\n    at Object.execFile (node:child_process:351:17)\n    at A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\lib\\util.js:9:20\n    at new Promise (<anonymous>)\n    at PythonFinder.execFile (A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\lib\\util.js:8:37)\n    at PythonFinder.run (A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\lib\\find-python.js:265:48)\n    at PythonFinder.checkExecPath (A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\lib\\find-python.js:230:34)\n    at PythonFinder.checkCommand (A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\lib\\find-python.js:189:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
npm error gyp verb find Python Python is not set from environment variable PYTHON
npm error gyp verb find Python checking if the py launcher can be used to find Python 3
npm error gyp verb find Python - executing "py.exe" to get Python 3 executable path
npm error gyp sill find Python execFile: exec = "py.exe"
npm error gyp sill find Python execFile: args = ["-3","-c","import sys; sys.stdout.buffer.write(sys.executable.encode('utf-8'));"]
npm error gyp sill find Python execFile: opts = {"env":{"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","BUNDLED_DEBUGPY_PATH":"c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\libs\\debugpy","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133895900671599873","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_16144_KTWRETPXFFGHLRQY","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"WORKSTATION-149","COMSPEC":"C:\\Windows\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\Windows\\notepad.exe","EFC_9824":"1","EXEPATH":"C:\\Program Files\\Git\\bin","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","HOME":"C:\\Users\\<USER>\\Users\\Chris","HOSTNAME":"Workstation-14900K","IGCCSVC_DB":"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAtu7b3Q6oNkyHKP52rq/BFgQAAAACAAAAAAAQZgAAAAEAACAAAAALAwF36SphtnzBh2f2KU7lUREn03F1uo0ViG/mVWzblAAAAAAOgAAAAAIAACAAAADjUGRkNxLz2LNxY1092QCyUeQepBCFc4gds4b8cssiemAAAACu68cd/vsYxgUsq/by4jkPcNF4PqnOxaEMvMEFySyLIBRGioTIgeyKYkcyJXoUM5+31eSOCAa82JWKZuaJYBwMJwXgwG93WK7YR5lICDMLr6EgQQItL6KLuE7WvPoNb55AAAAARv4dNJQ7j5KmaAHQJ15zn7V/oii9SsRvvvRsZ11dDJf2Init8DcFuFteZcDbR2UzQshWVRJg/sAnNS+RSpvNsA==","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\WORKSTATION-149","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","npm_command":"install","npm_config_better_sqlite3_binary_host_mirror":"https://github.com/WiseLibs/better-sqlite3/releases/download","npm_config_build_from_source":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_electron_mirror":"https://github.com/electron/electron/releases/download/v","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_local_prefix":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","npm_config_msvs_version":"2022","npm_config_node_gyp":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_noproxy":"","npm_config_npm_version":"11.0.0","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_python":"C:\\Program Files\\Python311\\python.exe","npm_config_runtime":"electron","npm_config_save_exact":"true","npm_config_target":"35.1.2","npm_config_target_arch":"x64","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/11.0.0 node/v20.19.0 win32 x64 workspaces/false","npm_execpath":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"install","npm_lifecycle_script":"prebuild-install || node-gyp rebuild --release","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_dev":"","npm_package_dev_optional":"","npm_package_integrity":"sha512-JhTZjpyapA1icCEjIZB4TSSgkGdFgpWZA2Wszg7Cf4JwJwKQmbvuNnJBeR+EYG/Z29OXvR4G//Rbg31BW/Z7Yg==","npm_package_json":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\package.json","npm_package_name":"better-sqlite3","npm_package_optional":"","npm_package_peer":"","npm_package_resolved":"https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.0.1.tgz","npm_package_version":"8.0.1","NUMBER_OF_PROCESSORS":"32","NVAR_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA AR SDK\\models","NVM_HOME":"C:\\Users\\<USER>\\AppData\\Local\\nvm","NVM_SYMLINK":"C:\\nvm4w\\nodejs","NVVFX_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA Video Effects\\models","OneDrive":"A:\\OneDrive - Lydian Ledgers Ltd","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PATH":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\node_modules\\.bin;A:\\WEBSITES\\node_modules\\.bin;A:\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 183 Stepping 1, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"b701","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\Windows","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"dumb","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.99.3","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"WORKSTATION-149","USERDOMAIN_ROAMINGPROFILE":"WORKSTATION-149","USERNAME":"Chris","USERPROFILE":"C:\\Users\\<USER>\\Users\\Chris\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-88ddf9b0884b2b39.txt","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-65bebf4f32-sock","WINDIR":"C:\\Windows","ZES_ENABLE_SYSMAN":"1","_":"C:/Program Files/nodejs/node.exe"},"shell":false}
npm error gyp sill find Python execFile result: err = null
npm error gyp sill find Python execFile result: stdout = "C:\\Program Files\\Python313\\python.exe"
npm error gyp sill find Python execFile result: stderr = ""
npm error gyp verb find Python - executable path is "C:\Program Files\Python313\python.exe"
npm error gyp verb find Python - executing "C:\Program Files\Python313\python.exe" to get version
npm error gyp sill find Python execFile: exec = "C:\\Program Files\\Python313\\python.exe"
npm error gyp sill find Python execFile: args = ["-c","import sys; print(\"%s.%s.%s\" % sys.version_info[:3]);"]
npm error gyp sill find Python execFile: opts = {"env":{"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","BUNDLED_DEBUGPY_PATH":"c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\libs\\debugpy","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133895900671599873","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_16144_KTWRETPXFFGHLRQY","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"WORKSTATION-149","COMSPEC":"C:\\Windows\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\Windows\\notepad.exe","EFC_9824":"1","EXEPATH":"C:\\Program Files\\Git\\bin","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","HOME":"C:\\Users\\<USER>\\Users\\Chris","HOSTNAME":"Workstation-14900K","IGCCSVC_DB":"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAtu7b3Q6oNkyHKP52rq/BFgQAAAACAAAAAAAQZgAAAAEAACAAAAALAwF36SphtnzBh2f2KU7lUREn03F1uo0ViG/mVWzblAAAAAAOgAAAAAIAACAAAADjUGRkNxLz2LNxY1092QCyUeQepBCFc4gds4b8cssiemAAAACu68cd/vsYxgUsq/by4jkPcNF4PqnOxaEMvMEFySyLIBRGioTIgeyKYkcyJXoUM5+31eSOCAa82JWKZuaJYBwMJwXgwG93WK7YR5lICDMLr6EgQQItL6KLuE7WvPoNb55AAAAARv4dNJQ7j5KmaAHQJ15zn7V/oii9SsRvvvRsZ11dDJf2Init8DcFuFteZcDbR2UzQshWVRJg/sAnNS+RSpvNsA==","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\WORKSTATION-149","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","npm_command":"install","npm_config_better_sqlite3_binary_host_mirror":"https://github.com/WiseLibs/better-sqlite3/releases/download","npm_config_build_from_source":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_electron_mirror":"https://github.com/electron/electron/releases/download/v","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_local_prefix":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll","npm_config_msvs_version":"2022","npm_config_node_gyp":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_noproxy":"","npm_config_npm_version":"11.0.0","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_python":"C:\\Program Files\\Python311\\python.exe","npm_config_runtime":"electron","npm_config_save_exact":"true","npm_config_target":"35.1.2","npm_config_target_arch":"x64","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/11.0.0 node/v20.19.0 win32 x64 workspaces/false","npm_execpath":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"install","npm_lifecycle_script":"prebuild-install || node-gyp rebuild --release","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_dev":"","npm_package_dev_optional":"","npm_package_integrity":"sha512-JhTZjpyapA1icCEjIZB4TSSgkGdFgpWZA2Wszg7Cf4JwJwKQmbvuNnJBeR+EYG/Z29OXvR4G//Rbg31BW/Z7Yg==","npm_package_json":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\package.json","npm_package_name":"better-sqlite3","npm_package_optional":"","npm_package_peer":"","npm_package_resolved":"https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.0.1.tgz","npm_package_version":"8.0.1","NUMBER_OF_PROCESSORS":"32","NVAR_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA AR SDK\\models","NVM_HOME":"C:\\Users\\<USER>\\AppData\\Local\\nvm","NVM_SYMLINK":"C:\\nvm4w\\nodejs","NVVFX_MODEL_DIR":"C:\\Program Files\\NVIDIA Corporation\\NVIDIA Video Effects\\models","OneDrive":"A:\\OneDrive - Lydian Ledgers Ltd","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PATH":"A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\.bin;A:\\WEBSITES\\Payroll_software-V2\\node_modules\\.bin;A:\\WEBSITES\\node_modules\\.bin;A:\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311;C:\\Program Files\\Python313\\Scripts;C:\\Program Files\\Python313;C:\\Python312\\Scripts;C:\\Python312;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;%NVM_HOME%;%NVM_SYMLINK%;C:\\Program Files\\nodejs;C:\\Program Files\\Stripe;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 183 Stepping 1, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"b701","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\Windows","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"dumb","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.99.3","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"WORKSTATION-149","USERDOMAIN_ROAMINGPROFILE":"WORKSTATION-149","USERNAME":"Chris","USERPROFILE":"C:\\Users\\<USER>\\Users\\Chris\\.vscode\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-88ddf9b0884b2b39.txt","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-65bebf4f32-sock","WINDIR":"C:\\Windows","ZES_ENABLE_SYSMAN":"1","_":"C:/Program Files/nodejs/node.exe"},"shell":false}
npm error gyp sill find Python execFile result: err = null
npm error gyp sill find Python execFile result: stdout = "3.13.3\r\n"
npm error gyp sill find Python execFile result: stderr = ""
npm error gyp verb find Python - version is "3.13.3"
npm error gyp info find Python using Python version 3.13.3 found at "C:\Program Files\Python313\python.exe"
npm error gyp verb get node dir compiling against --target node version: 35.1.2
npm error gyp verb install input version string "35.1.2"
npm error gyp verb install installing version: 35.1.2
npm error gyp verb install --ensure was passed, so won't reinstall if already installed
npm error gyp verb install version is already installed, need to check "installVersion"
npm error gyp verb got "installVersion" 11
npm error gyp verb needs "installVersion" 11
npm error gyp verb install version is good
npm error gyp verb on Windows; need to check node.lib
npm error gyp verb get node dir target node version installed: 35.1.2
npm error gyp verb build dir attempting to create "build" dir: A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build
npm error gyp verb build dir "build" dir needed to be created? Yes
npm error gyp verb find VS msvs_version was set from command line or npm config
npm error gyp verb find VS - looking for Visual Studio version 2022
npm error gyp verb find VS VCINSTALLDIR not set, not running in VS Command Prompt
npm error gyp sill find VS Running C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe [
npm error gyp sill find VS '-NoProfile',
npm error gyp sill find VS '-Command',
npm error gyp sill find VS '&{@(Get-Module -ListAvailable -Name VSSetup).Version.ToString()}'
npm error gyp sill find VS ]
npm error gyp sill find VS Running C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe [
npm error gyp sill find VS '-NoProfile',
npm error gyp sill find VS '-Command',
npm error gyp sill find VS '&{Get-VSSetupInstance  | ConvertTo-Json -Depth 3}'
npm error gyp sill find VS ]
npm error gyp sill find VS PS stderr = "Get-VSSetupInstance : The term 'Get-VSSetupInstance' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the \r\nspelling of the name, or if a path was included, verify that the path is correct and try again.\r\nAt line:1 char:3\r\n+ &{Get-VSSetupInstance  | ConvertTo-Json -Depth 3}\r\n+   ~~~~~~~~~~~~~~~~~~~\r\n    + CategoryInfo          : ObjectNotFound: (Get-VSSetupInstance:String) [], CommandNotFoundException\r\n    + FullyQualifiedErrorId : CommandNotFoundException\r\n \r\n"
npm error gyp sill find VS PS stdout = ""
npm error gyp sill find VS SyntaxError: Unexpected end of JSON input
npm error gyp sill find VS at JSON.parse (<anonymous>)
npm error gyp sill find VS at VisualStudioFinder.parseData (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\find-visualstudio.js:286:21)
npm error gyp sill find VS at VisualStudioFinder.findNewVSUsingSetupModule (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\find-visualstudio.js:202:27)
npm error gyp sill find VS at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
npm error gyp sill find VS at async VisualStudioFinder.findVisualStudio (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\find-visualstudio.js:68:20)
npm error gyp sill find VS at async createBuildDir (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\configure.js:112:18)
npm error gyp sill find VS at async run (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\bin\node-gyp.js:81:18)
npm error gyp verb find VS could not use PowerShell to find Visual Studio 2017 or newer, try re-running with '--loglevel silly' for more details.      
npm error gyp verb find VS
npm error gyp verb find VS Failure details: undefined
npm error gyp sill find VS Running C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe [
npm error gyp sill find VS '-ExecutionPolicy',
npm error gyp sill find VS 'Unrestricted',
npm error gyp sill find VS '-NoProfile',
npm error gyp sill find VS '-Command',
npm error gyp sill find VS "&{Add-Type -Path 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\lib\\Find-VisualStudio.cs';[VisualStudioConfiguration.Main]::PrintJson()}"
npm error gyp sill find VS ]
npm error gyp sill find VS PS stderr = ""
npm error gyp sill find VS processing installation: "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools"
npm error gyp sill find VS - version match = ["17.13.35931.197","17","13"]
npm error gyp sill find VS - found VC.Tools.x86.x64
npm error gyp sill find VS - found Win10/11SDK: 22621
npm error gyp sill find VS vsInfo: [
npm error gyp sill find VS {
npm error gyp sill find VS version: '17.13.35931.197',
npm error gyp sill find VS versionMajor: 17,
npm error gyp sill find VS versionMinor: 13,
npm error gyp sill find VS versionYear: 2022,
npm error gyp sill find VS path: 'C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools',
npm error gyp sill find VS msBuild: 'C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\MSBuild\\Current\\Bin\\MSBuild.exe',
npm error gyp sill find VS toolset: 'v143',
npm error gyp sill find VS sdk: '10.0.22621.0'
npm error gyp sill find VS }
npm error gyp sill find VS ]
npm error gyp verb find VS checking VS2022 (17.13.35931.197) found at:
npm error gyp verb find VS "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools"
npm error gyp verb find VS - found "Visual Studio C++ core features"
npm error gyp verb find VS - found VC++ toolset: v143
npm error gyp verb find VS - found Windows SDK: 10.0.22621.0
npm error gyp info find VS using VS2022 (17.13.35931.197) found at:
npm error gyp info find VS "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools"
npm error gyp info find VS run with --verbose for detailed information
npm error gyp verb build/config.gypi creating config file
npm error gyp sill build/config.gypi {
npm error gyp sill build/config.gypi target_defaults: {
npm error gyp sill build/config.gypi cflags: [],
npm error gyp sill build/config.gypi default_configuration: 'Release',
npm error gyp sill build/config.gypi defines: [],
npm error gyp sill build/config.gypi include_dirs: [],
npm error gyp sill build/config.gypi libraries: [],
npm error gyp sill build/config.gypi msbuild_toolset: 'v143',
npm error gyp sill build/config.gypi msvs_windows_target_platform_version: '10.0.22621.0'
npm error gyp sill build/config.gypi },
npm error gyp sill build/config.gypi variables: {
npm error gyp sill build/config.gypi asan: 0,
npm error gyp sill build/config.gypi clang: 0,
npm error gyp sill build/config.gypi coverage: false,
npm error gyp sill build/config.gypi dcheck_always_on: 0,
npm error gyp sill build/config.gypi debug_nghttp2: false,
npm error gyp sill build/config.gypi debug_node: false,
npm error gyp sill build/config.gypi enable_lto: false,
npm error gyp sill build/config.gypi enable_pgo_generate: false,
npm error gyp sill build/config.gypi enable_pgo_use: false,
npm error gyp sill build/config.gypi error_on_warn: false,
npm error gyp sill build/config.gypi force_dynamic_crt: 0,
npm error gyp sill build/config.gypi host_arch: 'x64',
npm error gyp sill build/config.gypi icu_data_in: '..\\..\\deps\\icu-tmp\\icudt76l.dat',
npm error gyp sill build/config.gypi icu_endianness: 'l',
npm error gyp sill build/config.gypi icu_gyp_path: 'tools/icu/icu-generic.gyp',
npm error gyp sill build/config.gypi icu_path: 'deps/icu-small',
npm error gyp sill build/config.gypi icu_small: false,
npm error gyp sill build/config.gypi icu_ver_major: '76',
npm error gyp sill build/config.gypi is_debug: 0,
npm error gyp sill build/config.gypi libdir: 'lib',
npm error gyp sill build/config.gypi llvm_version: '0.0',
npm error gyp sill build/config.gypi napi_build_version: '9',
npm error gyp sill build/config.gypi nasm_version: '2.16',
npm error gyp sill build/config.gypi node_builtin_shareable_builtins: [
npm error gyp sill build/config.gypi 'deps/cjs-module-lexer/lexer.js',
npm error gyp sill build/config.gypi 'deps/cjs-module-lexer/dist/lexer.js',
npm error gyp sill build/config.gypi 'deps/undici/undici.js'
npm error gyp sill build/config.gypi ],
npm error gyp sill build/config.gypi node_byteorder: 'little',
npm error gyp sill build/config.gypi node_debug_lib: false,
npm error gyp sill build/config.gypi node_enable_d8: false,
npm error gyp sill build/config.gypi node_enable_v8_vtunejit: false,
npm error gyp sill build/config.gypi node_fipsinstall: false,
npm error gyp sill build/config.gypi node_install_corepack: true,
npm error gyp sill build/config.gypi node_install_npm: true,
npm error gyp sill build/config.gypi node_library_files: [
npm error gyp sill build/config.gypi 'lib/_http_agent.js',
npm error gyp sill build/config.gypi 'lib/_http_client.js',
npm error gyp sill build/config.gypi 'lib/_http_common.js',
npm error gyp sill build/config.gypi 'lib/_http_incoming.js',
npm error gyp sill build/config.gypi 'lib/_http_outgoing.js',
npm error gyp sill build/config.gypi 'lib/_http_server.js',
npm error gyp sill build/config.gypi 'lib/_stream_duplex.js',
npm error gyp sill build/config.gypi 'lib/_stream_passthrough.js',
npm error gyp sill build/config.gypi 'lib/_stream_readable.js',
npm error gyp sill build/config.gypi 'lib/_stream_transform.js',
npm error gyp sill build/config.gypi 'lib/_stream_wrap.js',
npm error gyp sill build/config.gypi 'lib/_stream_writable.js',
npm error gyp sill build/config.gypi 'lib/_tls_common.js',
npm error gyp sill build/config.gypi 'lib/_tls_wrap.js',
npm error gyp sill build/config.gypi 'lib/assert.js',
npm error gyp sill build/config.gypi 'lib/assert/strict.js',
npm error gyp sill build/config.gypi 'lib/async_hooks.js',
npm error gyp sill build/config.gypi 'lib/buffer.js',
npm error gyp sill build/config.gypi 'lib/child_process.js',
npm error gyp sill build/config.gypi 'lib/cluster.js',
npm error gyp sill build/config.gypi 'lib/console.js',
npm error gyp sill build/config.gypi 'lib/constants.js',
npm error gyp sill build/config.gypi 'lib/crypto.js',
npm error gyp sill build/config.gypi 'lib/dgram.js',
npm error gyp sill build/config.gypi 'lib/diagnostics_channel.js',
npm error gyp sill build/config.gypi 'lib/dns.js',
npm error gyp sill build/config.gypi 'lib/dns/promises.js',
npm error gyp sill build/config.gypi 'lib/domain.js',
npm error gyp sill build/config.gypi 'lib/events.js',
npm error gyp sill build/config.gypi 'lib/fs.js',
npm error gyp sill build/config.gypi 'lib/fs/promises.js',
npm error gyp sill build/config.gypi 'lib/http.js',
npm error gyp sill build/config.gypi 'lib/http2.js',
npm error gyp sill build/config.gypi 'lib/https.js',
npm error gyp sill build/config.gypi 'lib/inspector.js',
npm error gyp sill build/config.gypi 'lib/inspector/promises.js',
npm error gyp sill build/config.gypi 'lib/internal/abort_controller.js',
npm error gyp sill build/config.gypi 'lib/internal/assert.js',
npm error gyp sill build/config.gypi 'lib/internal/assert/assertion_error.js',
npm error gyp sill build/config.gypi 'lib/internal/assert/calltracker.js',
npm error gyp sill build/config.gypi 'lib/internal/assert/utils.js',
npm error gyp sill build/config.gypi 'lib/internal/async_hooks.js',
npm error gyp sill build/config.gypi 'lib/internal/blob.js',
npm error gyp sill build/config.gypi 'lib/internal/blocklist.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/node.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/realm.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/shadow_realm.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/switches/does_not_own_process_state.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/switches/does_own_process_state.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/switches/is_main_thread.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/switches/is_not_main_thread.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/web/exposed-wildcard.js',
npm error gyp sill build/config.gypi 'lib/internal/bootstrap/web/exposed-window-or-worker.js',
npm error gyp sill build/config.gypi 'lib/internal/buffer.js',
npm error gyp sill build/config.gypi 'lib/internal/child_process.js',
npm error gyp sill build/config.gypi 'lib/internal/child_process/serialization.js',
npm error gyp sill build/config.gypi 'lib/internal/cli_table.js',
npm error gyp sill build/config.gypi 'lib/internal/cluster/child.js',
npm error gyp sill build/config.gypi 'lib/internal/cluster/primary.js',
npm error gyp sill build/config.gypi 'lib/internal/cluster/round_robin_handle.js',
npm error gyp sill build/config.gypi 'lib/internal/cluster/shared_handle.js',
npm error gyp sill build/config.gypi 'lib/internal/cluster/utils.js',
npm error gyp sill build/config.gypi 'lib/internal/cluster/worker.js',
npm error gyp sill build/config.gypi 'lib/internal/console/constructor.js',
npm error gyp sill build/config.gypi 'lib/internal/console/global.js',
npm error gyp sill build/config.gypi 'lib/internal/constants.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/aes.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/certificate.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/cfrg.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/cipher.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/diffiehellman.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/ec.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/hash.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/hashnames.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/hkdf.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/keygen.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/keys.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/mac.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/pbkdf2.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/random.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/rsa.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/scrypt.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/sig.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/util.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/webcrypto.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/webidl.js',
npm error gyp sill build/config.gypi 'lib/internal/crypto/x509.js',
npm error gyp sill build/config.gypi 'lib/internal/debugger/inspect.js',
npm error gyp sill build/config.gypi 'lib/internal/debugger/inspect_client.js',
npm error gyp sill build/config.gypi 'lib/internal/debugger/inspect_repl.js',
npm error gyp sill build/config.gypi 'lib/internal/dgram.js',
npm error gyp sill build/config.gypi 'lib/internal/dns/callback_resolver.js',
npm error gyp sill build/config.gypi 'lib/internal/dns/promises.js',
npm error gyp sill build/config.gypi 'lib/internal/dns/utils.js',
npm error gyp sill build/config.gypi 'lib/internal/encoding.js',
npm error gyp sill build/config.gypi 'lib/internal/error_serdes.js',
npm error gyp sill build/config.gypi 'lib/internal/errors.js',
npm error gyp sill build/config.gypi 'lib/internal/event_target.js',
npm error gyp sill build/config.gypi 'lib/internal/events/abort_listener.js',
npm error gyp sill build/config.gypi 'lib/internal/events/symbols.js',
npm error gyp sill build/config.gypi ... 218 more items
npm error gyp sill build/config.gypi ],
npm error gyp sill build/config.gypi node_module_version: 115,
npm error gyp sill build/config.gypi node_no_browser_globals: false,
npm error gyp sill build/config.gypi node_prefix: '\\usr\\local',
npm error gyp sill build/config.gypi node_release_urlbase: 'https://nodejs.org/download/release/',
npm error gyp sill build/config.gypi node_shared: false,
npm error gyp sill build/config.gypi node_shared_ada: false,
npm error gyp sill build/config.gypi node_shared_brotli: false,
npm error gyp sill build/config.gypi node_shared_cares: false,
npm error gyp sill build/config.gypi node_shared_http_parser: false,
npm error gyp sill build/config.gypi node_shared_libuv: false,
npm error gyp sill build/config.gypi node_shared_nghttp2: false,
npm error gyp sill build/config.gypi node_shared_nghttp3: false,
npm error gyp sill build/config.gypi node_shared_ngtcp2: false,
npm error gyp sill build/config.gypi node_shared_openssl: false,
npm error gyp sill build/config.gypi node_shared_simdjson: false,
npm error gyp sill build/config.gypi node_shared_simdutf: false,
npm error gyp sill build/config.gypi node_shared_uvwasi: false,
npm error gyp sill build/config.gypi node_shared_zlib: false,
npm error gyp sill build/config.gypi node_tag: '',
npm error gyp sill build/config.gypi node_target_type: 'executable',
npm error gyp sill build/config.gypi node_use_bundled_v8: true,
npm error gyp sill build/config.gypi node_use_node_code_cache: true,
npm error gyp sill build/config.gypi node_use_node_snapshot: true,
npm error gyp sill build/config.gypi node_use_openssl: true,
npm error gyp sill build/config.gypi node_use_v8_platform: true,
npm error gyp sill build/config.gypi node_with_ltcg: true,
npm error gyp sill build/config.gypi node_without_node_options: false,
npm error gyp sill build/config.gypi node_write_snapshot_as_array_literals: true,
npm error gyp sill build/config.gypi openssl_is_fips: false,
npm error gyp sill build/config.gypi openssl_quic: true,
npm error gyp sill build/config.gypi ossfuzz: false,
npm error gyp sill build/config.gypi shlib_suffix: 'so.115',
npm error gyp sill build/config.gypi single_executable_application: true,
npm error gyp sill build/config.gypi target_arch: 'x64',
npm error gyp sill build/config.gypi ubsan: 0,
npm error gyp sill build/config.gypi use_prefix_to_find_headers: false,
npm error gyp sill build/config.gypi v8_enable_31bit_smis_on_64bit_arch: 0,
npm error gyp sill build/config.gypi v8_enable_extensible_ro_snapshot: 0,
npm error gyp sill build/config.gypi v8_enable_gdbjit: 0,
npm error gyp sill build/config.gypi v8_enable_hugepage: 0,
npm error gyp sill build/config.gypi v8_enable_i18n_support: 1,
npm error gyp sill build/config.gypi v8_enable_inspector: 1,
npm error gyp sill build/config.gypi v8_enable_javascript_promise_hooks: 1,
npm error gyp sill build/config.gypi v8_enable_lite_mode: 0,
npm error gyp sill build/config.gypi v8_enable_maglev: 0,
npm error gyp sill build/config.gypi v8_enable_object_print: 1,
npm error gyp sill build/config.gypi v8_enable_pointer_compression: 0,
npm error gyp sill build/config.gypi v8_enable_sandbox: 0,
npm error gyp sill build/config.gypi v8_enable_shared_ro_heap: 1,
npm error gyp sill build/config.gypi v8_enable_short_builtin_calls: 1,
npm error gyp sill build/config.gypi v8_enable_v8_checks: 0,
npm error gyp sill build/config.gypi v8_enable_webassembly: 1,
npm error gyp sill build/config.gypi v8_no_strict_aliasing: 1,
npm error gyp sill build/config.gypi v8_optimized_debug: 1,
npm error gyp sill build/config.gypi v8_promise_internal_field_count: 1,
npm error gyp sill build/config.gypi v8_random_seed: 0,
npm error gyp sill build/config.gypi v8_trace_maps: 0,
npm error gyp sill build/config.gypi v8_use_siphash: 1,
npm error gyp sill build/config.gypi want_separate_host_toolset: 0,
npm error gyp sill build/config.gypi nodedir: 'C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\35.1.2',
npm error gyp sill build/config.gypi python: 'C:\\Program Files\\Python313\\python.exe',
npm error gyp sill build/config.gypi standalone_static_library: 1,
npm error gyp sill build/config.gypi msbuild_path: 'C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\MSBuild\\Current\\Bin\\MSBuild.exe',
npm error gyp sill build/config.gypi better_sqlite3_binary_host_mirror: 'https://github.com/WiseLibs/better-sqlite3/releases/download',
npm error gyp sill build/config.gypi build_from_source: 'true',
npm error gyp sill build/config.gypi cache: 'C:\\Users\\<USER>\\AppData\\Local\\npm-cache',
npm error gyp sill build/config.gypi electron_mirror: 'https://github.com/electron/electron/releases/download/v',
npm error gyp sill build/config.gypi globalconfig: 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc',
npm error gyp sill build/config.gypi global_prefix: 'C:\\Users\\<USER>\\AppData\\Roaming\\npm',
npm error gyp sill build/config.gypi init_module: 'C:\\Users\\<USER>\\.npm-init.js',
npm error gyp sill build/config.gypi local_prefix: 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll',
npm error gyp sill build/config.gypi node_gyp: 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js', 
npm error gyp sill build/config.gypi npm_version: '11.0.0',
npm error gyp sill build/config.gypi prefix: 'C:\\Users\\<USER>\\AppData\\Roaming\\npm',
npm error gyp sill build/config.gypi runtime: 'electron',
npm error gyp sill build/config.gypi save_exact: 'true',
npm error gyp sill build/config.gypi target: '35.1.2',
npm error gyp sill build/config.gypi userconfig: 'C:\\Users\\<USER>\\.npmrc',
npm error gyp sill build/config.gypi user_agent: 'npm/11.0.0 node/v20.19.0 win32 x64 workspaces/false'
npm error gyp sill build/config.gypi }
npm error gyp sill build/config.gypi }
npm error gyp verb build/config.gypi writing out config file: A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\build\config.gypi 
npm error gyp verb config.gypi checking for gypi file: A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\config.gypi
npm error gyp verb common.gypi checking for gypi file: A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3\common.gypi
npm error gyp verb gyp gyp format was not specified; forcing "msvs"
npm error gyp info spawn C:\Program Files\Python313\python.exe
npm error gyp info spawn args [
npm error gyp info spawn args 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\gyp\\gyp_main.py',
npm error gyp info spawn args 'binding.gyp',
npm error gyp info spawn args '-f',
npm error gyp info spawn args 'msvs',
npm error gyp info spawn args '-I',
npm error gyp info spawn args 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\build\\config.gypi',
npm error gyp info spawn args '-I',
npm error gyp info spawn args 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\addon.gypi',
npm error gyp info spawn args '-I',
npm error gyp info spawn args 'C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\35.1.2\\include\\node\\common.gypi',
npm error gyp info spawn args '-Dlibrary=shared_library',
npm error gyp info spawn args '-Dvisibility=default',
npm error gyp info spawn args '-Dnode_root_dir=C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\35.1.2',
npm error gyp info spawn args '-Dnode_gyp_dir=A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp',
npm error gyp info spawn args '-Dnode_lib_file=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\node-gyp\\\\Cache\\\\35.1.2\\\\<(target_arch)\\\\node.lib', 
npm error gyp info spawn args '-Dmodule_root_dir=A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3',
npm error gyp info spawn args '-Dnode_engine=v8',
npm error gyp info spawn args '--depth=.',
npm error gyp info spawn args '--no-parallel',
npm error gyp info spawn args '--generator-output',
npm error gyp info spawn args 'A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\better-sqlite3\\build',
npm error gyp info spawn args '-Goutput_dir=.'
npm error gyp info spawn args ]
npm error gyp verb build type Release
npm error gyp verb architecture x64
npm error gyp verb node dev dir C:\Users\<USER>\AppData\Local\node-gyp\Cache\35.1.2
npm error gyp verb python C:\Program Files\Python313\python.exe
npm error gyp verb found first Solution file build/binding.sln
npm error gyp verb using MSBuild: C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe
npm error gyp info spawn C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe
npm error gyp info spawn args [
npm error gyp info spawn args 'build/binding.sln',
npm error gyp info spawn args '/nologo',
npm error gyp info spawn args '/p:Configuration=Release;Platform=x64'
npm error gyp info spawn args ]
npm error gyp ERR! build error
npm error gyp ERR! stack Error: `C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe` failed with exit code: 1
npm error gyp ERR! stack at ChildProcess.<anonymous> (A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\@electron\node-gyp\lib\build.js:215:23)  
npm error gyp ERR! stack at ChildProcess.emit (node:events:524:28)
npm error gyp ERR! stack at ChildProcess._handle.onexit (node:internal/child_process:293:12)
npm error gyp ERR! System Windows_NT 10.0.22631
npm error gyp ERR! command "C:\\Program Files\\nodejs\\node.exe" "A:\\WEBSITES\\Payroll_software-V2\\uk-payroll\\node_modules\\@electron\\node-gyp\\bin\\node-gyp.js" "rebuild" "--release"
npm error gyp ERR! cwd A:\WEBSITES\Payroll_software-V2\uk-payroll\node_modules\better-sqlite3
npm error gyp ERR! node -v v20.19.0
npm error gyp ERR! node-gyp -v v10.2.0-electron.1
npm error gyp ERR! not ok
npm error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-04-20T09_27_48_521Z-debug-0.log