"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
  CSSProperties,
} from "react";
import { ArrowUp, ArrowDown, GripHorizontal } from "lucide-react";
import PayslipStatusBadge from "./payslip-status-badge";
import PayrollViewNavigation from "./PayrollViewNavigation";

import ViewNavigation from "./view-navigation";
// DnD imports
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  type DragEndEvent,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  type Updater,
  Header,
  Cell,
} from "@tanstack/react-table";

import {
  mockEmployees,
  transformToOverviewFormat,
  formatCurrency,
  parseCurrency,
} from "./shared-mock-data";

// Mock data for pay periods - will be used to get period details
const mockPayPeriods = [
  // Monthly periods - Tax year in UK runs from April 6 to April 5
  // April is month 1, March is month 12
  {
    id: "january-2025",
    name: "January",
    number: "10",
    type: "Month",
    status: "future",
    endDate: "31/01/2025",
    taxYear: "2024/25",
  },
  {
    id: "february-2025",
    name: "February",
    number: "11",
    type: "Month",
    status: "future",
    endDate: "28/02/2025",
    taxYear: "2024/25",
  },
  {
    id: "march-2025",
    name: "March",
    number: "12",
    type: "Month",
    status: "open",
    endDate: "31/03/2025",
    taxYear: "2024/25",
  },
  {
    id: "december-2024",
    name: "December",
    number: "9",
    type: "Month",
    status: "closed",
    endDate: "31/12/2024",
    taxYear: "2024/25",
  },
  {
    id: "november-2024",
    name: "November",
    number: "8",
    type: "Month",
    status: "closed",
    endDate: "30/11/2024",
    taxYear: "2024/25",
  },
  {
    id: "october-2024",
    name: "October",
    number: "7",
    type: "Month",
    status: "closed",
    endDate: "31/10/2024",
    taxYear: "2024/25",
  },

  // Weekly periods - Tax year in UK runs from April 6 to April 5
  // Week 1 starts after April 5th, Week 53 (if exists) ends on April 5th
  {
    id: "week-52-2025",
    name: "52",
    number: "52",
    type: "Week",
    status: "open",
    endDate: "04/04/2025",
    taxYear: "2024/25",
  },
  {
    id: "week-53-2025",
    name: "53",
    number: "53",
    type: "Week",
    status: "future",
    endDate: "05/04/2025",
    taxYear: "2024/25",
  },
  {
    id: "week-1-2026",
    name: "1",
    number: "1",
    type: "Week",
    status: "future",
    endDate: "11/04/2025",
    taxYear: "2025/26",
  },
];

// Draggable header component
const DraggableTableHeader = React.forwardRef<
  HTMLTableCellElement,
  { header: Header<any, unknown> }
>(({ header }, ref) => {
  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useSortable({
      id: header.column.id || `header-${header.id}`,
    });
  DraggableTableHeader.displayName = "DraggableTableHeader";

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    color: isDragging ? "#006EFFFF" : "",
    position: "relative",
    transform: CSS.Translate.toString(transform),
    transition: "width transform 0.2s ease-in-out",
    whiteSpace: "wrap",
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <th
      key={header.id}
      ref={(el) => {
        setNodeRef(el);
        if (typeof ref === "function") ref(el);
        // Do not assign to ref.current (read-only)
      }}
      className="font-normal text-slate-600 dark:text-sky-300 [&:has([role=checkbox])]:pr-0"
      style={{
        ...style,
        textAlign: header.column.id === "name" ? "left" : "center",
      }}
    >
      <div
        className={`flex flex-col ${header.column.id === "name" ? "items-start" : "items-center"}`}
      >
        {/* Header content with sort indicator */}
        <div
          className={`flex items-center gap-1 ${header.column.id === "status" ? "w-full justify-center" : header.column.id === "name" ? "justify-start" : "justify-center"} ${header.column.getCanSort() ? "cursor-pointer select-none" : ""}`}
          onClick={header.column.getToggleSortingHandler()}
        >
          {flexRender(header.column.columnDef.header, header.getContext())}
          {header.column.getIsSorted() && (
            <div>
              {header.column.getIsSorted() === "asc" ? (
                <ArrowUp className="h-4 w-4" />
              ) : (
                <ArrowDown className="h-4 w-4" />
              )}
            </div>
          )}
        </div>

        {/* Drag handle below the header content - or empty space for fixed columns */}
        {header.column.id !== "name" && header.column.id !== "status" ? (
          <button
            {...attributes}
            {...listeners}
            className="mt-1 cursor-move p-1 opacity-80 hover:text-sky-500 hover:opacity-100"
            onClick={(e) => e.stopPropagation()} // Prevent sorting when clicking the drag handle
            data-drag-handle="true"
          >
            <GripHorizontal className="h-3 w-3" />
          </button>
        ) : (
          <div className="mt-1 h-3"></div> /* Empty space to maintain alignment */
        )}
      </div>
    </th>
  );
});

// Cell component that moves with the header during dragging
const DragAlongCell = ({
  cell,
  isFirstRow = false,
  columnWidths,
}: {
  cell: Cell<any, unknown>;
  isFirstRow?: boolean;
  columnWidths: Record<string, number>;
}) => {
  const { isDragging, setNodeRef, transform } = useSortable({
    id: cell.column.id || `cell-${cell.id}`,
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.6 : 1,
    color: isDragging ? "#006EFFFF" : "",
    position: "relative",
    transform: CSS.Translate.toString(transform),
    transition: "width transform 0.2s ease-in-out",
    zIndex: isDragging ? 1 : 0,
    paddingTop: isFirstRow ? "0px" : undefined,
  };

  return (
    <td
      ref={setNodeRef}
      className=""
      style={{
        width: columnWidths[cell.column.id]
          ? `${columnWidths[cell.column.id]}px`
          : undefined,
        ...style,
        textAlign: cell.column.id === "name" ? "left" : "center",
      }}
    >
      {flexRender(cell.column.columnDef.cell, cell.getContext())}
    </td>
  );
};

// Define employee type for type safety
type Employee = {
  id: string;
  name: string;
  status: string;
  basic: string;
  hours: string;
  additions: string;
  deductions: string;
  employeePension: string;
  employerPension: string;
  tax: string;
  ni: string;
  net: string;
  cost: string;
};

// Transform the shared mock data to the format needed for the overview
const overviewEmployees: Employee[] = transformToOverviewFormat(mockEmployees);

// Calculate totals for the overview based on filtered employees
const calculateTotals = (employees: Employee[]) => {
  return employees.reduce(
    (acc, employee) => {
      return {
        basic: acc.basic + parseCurrency(employee.basic),
        hours: acc.hours + parseInt(employee.hours),
        additions: acc.additions + parseCurrency(employee.additions),
        deductions: acc.deductions + parseCurrency(employee.deductions),
        employeePension:
          acc.employeePension + parseCurrency(employee.employeePension),
        employerPension:
          acc.employerPension + parseCurrency(employee.employerPension),
        tax: acc.tax + parseCurrency(employee.tax),
        ni: acc.ni + parseCurrency(employee.ni),
        net: acc.net + parseCurrency(employee.net),
        cost: acc.cost + parseCurrency(employee.cost),
      };
    },
    {
      basic: 0,
      hours: 0,
      additions: 0,
      deductions: 0,
      employeePension: 0,
      employerPension: 0,
      tax: 0,
      ni: 0,
      net: 0,
      cost: 0,
    },
  );
};

interface PayrollOverviewProps {
  periodId: string;
  onEmployeeSelect: (employeeId: string) => void;
  onSwitchToBatch?: () => void;
  onSwitchToOverview?: () => void;
  activeView?: string;
  // Props for persisting state
  initialStatusFilter?: "all" | "open" | "closed";
  onStatusFilterChange?: (filter: "all" | "open" | "closed") => void;
  initialSearchTerm?: string;
  onSearchTermChange?: (term: string) => void;
  initialSorting?: { id: string; desc: boolean }[];
  handleSortingChange?: (sorting: { id: string; desc: boolean }[]) => void;
}

// Define column definitions for TanStack Table
const columns: ColumnDef<Employee>[] = [
  {
    id: "name",
    accessorFn: (row) => row.name,
    header: "Employee",
    cell: (info) => (
      <div className="cursor-pointer text-sm">{info.getValue<string>()}</div>
    ),
    footer: () => <div className=""></div>,
  },
  {
    id: "status",
    accessorFn: (row) => row.status,
    header: "Status",
    cell: (info) => {
      const status = info.getValue() as string;
      return (
        <div className="flex justify-center">
          <PayslipStatusBadge status={status} />
        </div>
      );
    },
    footer: () => <div className="">Totals {">>>"}</div>,
  },
  {
    id: "basic",
    accessorKey: "basic",
    header: "Basic Pay",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.basic);
    },
  },
  {
    id: "hours",
    accessorKey: "hours",
    header: "Hours",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return totals.hours;
    },
  },
  {
    id: "additions",
    accessorKey: "additions",
    header: "Additions",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.additions);
    },
  },
  {
    id: "deductions",
    accessorKey: "deductions",
    header: "Deductions",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.deductions);
    },
  },
  {
    id: "employeePension",
    accessorKey: "employeePension",
    header: "Employee Pension",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.employeePension);
    },
  },
  {
    id: "employerPension",
    accessorKey: "employerPension",
    header: "Employer Pension",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.employerPension);
    },
  },
  {
    id: "tax",
    accessorKey: "tax",
    header: "Tax",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.tax);
    },
  },
  {
    id: "ni",
    accessorKey: "ni",
    header: "NI",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.ni);
    },
  },
  {
    id: "net",
    accessorKey: "net",
    header: "Net Pay",
    cell: (info) => <div className="">{info.getValue<string>()}</div>,
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.net);
    },
  },
  {
    id: "cost",
    accessorKey: "cost",
    header: "Cost",
    footer: ({ table }) => {
      const totals = calculateTotals(
        table.getFilteredRowModel().rows.map((row) => row.original),
      );
      return formatCurrency(totals.cost);
    },
  },
];

const PayrollOverview: React.FC<PayrollOverviewProps> = ({
  periodId,
  onEmployeeSelect,
  onSwitchToBatch,
  onSwitchToOverview,
  activeView = "overview",
  initialStatusFilter = "all",
  onStatusFilterChange,
  initialSearchTerm = "",
  onSearchTermChange,
  initialSorting = [{ id: "name", desc: false }],
  handleSortingChange,
}) => {
  // State for status filter and search term
  const [statusFilter, setStatusFilter] = useState<"all" | "open" | "closed">(
    initialStatusFilter,
  );
  const [searchTerm, setSearchTerm] = useState<string>(initialSearchTerm);
  // Set default sorting to A-Z by employee name
  const [sorting, setSorting] = useState<SortingState>(initialSorting);

  // State for column order
  const [columnOrder, setColumnOrder] = useState<string[]>(() => {
    // Get all valid column IDs
    const allColumnIds = columns.map((col) => col.id as string).filter(Boolean);

    // Try to get the saved column order from localStorage
    const savedColumnOrder =
      typeof window !== "undefined"
        ? localStorage.getItem(
            `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_overview_column_order`,
          )
        : null;

    if (savedColumnOrder) {
      try {
        // Parse the saved column order
        const parsedOrder = JSON.parse(savedColumnOrder) as string[];

        // Filter out any null or undefined values
        const validParsedOrder = parsedOrder.filter(Boolean);

        // Ensure all columns are included (in case new columns were added)
        const missingColumns = allColumnIds.filter(
          (col) => !validParsedOrder.includes(col),
        );

        // Return the saved order with any missing columns appended
        return [...validParsedOrder, ...missingColumns];
      } catch (error) {
        console.error("Error parsing saved column order:", error);
      }
    }

    // Default to all column IDs in their original order
    return allColumnIds;
  });

  // Save column order to localStorage when it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_overview_column_order`,
        JSON.stringify(columnOrder),
      );
    }
  }, [columnOrder]);

  // Set up DnD sensors with proper configuration
  const sensors = useSensors(
    useSensor(MouseSensor, {
      // Lower the activation constraint threshold
      activationConstraint: {
        distance: 1, // 1px movement before drag starts
      },
    }),
    useSensor(TouchSensor, {
      // Lower the activation constraint threshold
      activationConstraint: {
        delay: 30, // 50ms delay before drag starts
        tolerance: 1, // 1px movement before drag starts
      },
    }),
    useSensor(KeyboardSensor, {}),
  );

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      setColumnOrder((columnOrder) => {
        const oldIndex = columnOrder.indexOf(active.id as string);
        const newIndex = columnOrder.indexOf(over.id as string);
        return arrayMove(columnOrder, oldIndex, newIndex);
      });
    }
  };

  // Get period details
  const periodDetails = useMemo(() => {
    const period = mockPayPeriods.find((p) => p.id === periodId);
    if (!period)
      return { type: "Unknown", number: "", endDate: "", taxYear: "" };
    return period;
  }, [periodId]);

  // Apply global filters (search and status)
  const filteredData = useMemo(() => {
    return overviewEmployees.filter((emp) => {
      // If there's a search term, ignore status filter and search all employees
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          emp.name.toLowerCase().includes(searchLower) ||
          emp.id.toLowerCase().includes(searchLower)
        );
      }
      // If no search term, apply status filter
      return statusFilter === "all" || emp.status === statusFilter;
    });
  }, [searchTerm, statusFilter]);

  // Custom handler for column sorting to prevent the unsorted state
  const internalHandleSortingChange = (
    updaterOrValue: Updater<SortingState>,
  ) => {
    setSorting((old) => {
      // Handle function updater
      const newSorting =
        typeof updaterOrValue === "function"
          ? updaterOrValue(old)
          : updaterOrValue;

      // If trying to clear sorting (enter unsorted state)
      if (newSorting.length === 0 && old.length > 0) {
        // Instead of clearing, toggle the direction on the same column
        const finalSorting = [{ id: old[0].id, desc: !old[0].desc }];
        // We'll notify the parent in a useEffect, not during render
        return finalSorting;
      }

      // We'll notify the parent in a useEffect, not during render
      return newSorting;
    });
  };

  // Effect to notify parent component of sorting changes
  useEffect(() => {
    if (handleSortingChange) {
      handleSortingChange(sorting);
    }
  }, [sorting, handleSortingChange]);

  // State for column widths
  const [columnWidths] = useState<Record<string, number>>({});
  const headerRefs = useRef<Record<string, HTMLTableCellElement | null>>({});

  // Initialize TanStack Table
  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      sorting,
      columnOrder,
    },
    onSortingChange: internalHandleSortingChange,
    onColumnOrderChange: setColumnOrder,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    enableSorting: true,
    sortingFns: {
      name: (rowA, rowB, columnId) => {
        const valueA = rowA.getValue(columnId) as string;
        const valueB = rowB.getValue(columnId) as string;
        return valueA.localeCompare(valueB);
      },
    },
    enableMultiSort: false,
  });

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToHorizontalAxis]}
      onDragEnd={handleDragEnd}
      sensors={sensors}
    >
      <PayrollViewNavigation
        activeView={activeView}
        onSwitchToOverview={onSwitchToOverview}
        onSwitchToBatch={onSwitchToBatch}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        onSearchTermChange={onSearchTermChange}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        onStatusFilterChange={onStatusFilterChange}
        periodDetails={periodDetails}
        ViewNavigation={ViewNavigation}
      />

      {/* Unified scrollable table container with sticky header/footer support */}

      <div className="mt-3 flex min-h-0 w-full flex-1 flex-col overflow-auto">
        <table className="w-full min-w-[1200px] table-fixed caption-bottom px-10 text-sm">
          <thead className="bg-background sticky top-0 z-10 [&_tr]:border-b-2 [&_tr]:border-slate-300">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                <SortableContext
                  items={columnOrder.filter(Boolean)}
                  strategy={horizontalListSortingStrategy}
                >
                  {headerGroup.headers.map((header) => (
                    <DraggableTableHeader
                      key={header.id}
                      header={header}
                      ref={(el) => {
                        headerRefs.current[header.id] = el;
                      }}
                    />
                  ))}
                </SortableContext>
              </tr>
            ))}
          </thead>

          {/* Table Body */}

          <tbody className="[&_tr:last-child]:border-0">
            {table.getRowModel().rows.map((row, rowIndex) => (
              <tr
                key={row.id}
                className="data-[state=selected]:bg-muted h-7 cursor-pointer border-b transition-colors even:bg-slate-100 hover:bg-slate-200 dark:even:bg-black/30 dark:hover:bg-zinc-800"
                onClick={() => onEmployeeSelect(row.original.id)}
              >
                <SortableContext
                  items={columnOrder.filter(Boolean)}
                  strategy={horizontalListSortingStrategy}
                >
                  {row.getVisibleCells().map((cell) => (
                    <DragAlongCell
                      key={cell.id}
                      cell={cell}
                      isFirstRow={rowIndex === 0}
                      columnWidths={columnWidths}
                    />
                  ))}
                </SortableContext>
              </tr>
            ))}
          </tbody>

          {/* Separate totals row table, horizontally scroll-synced */}

          <tfoot className="sticky bottom-0 z-20 h-10 border-t-2 border-slate-300 bg-white text-sky-600 dark:bg-[#202020] dark:text-slate-100">
            {table.getFooterGroups().map((footerGroup: any) => (
              <tr key={footerGroup.id}>
                {footerGroup.headers.map((header: any) => (
                  <td
                    key={header.id}
                    className="font-medium"
                    style={{
                      width: columnWidths[header.id]
                        ? `${columnWidths[header.id]}px`
                        : undefined,
                      textAlign:
                        header.column.id === "name" ? "left" : "center",
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.footer,
                          header.getContext(),
                        )}
                  </td>
                ))}
              </tr>
            ))}
          </tfoot>
        </table>
      </div>
    </DndContext>
  );
};

export default PayrollOverview;
