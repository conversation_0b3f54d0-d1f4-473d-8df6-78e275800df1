"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useEffect } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { PinIcon, EyeOffIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

import { PayPeriod as BasePayPeriod } from '@/drizzle/schema/employer/payPeriod';

type PayPeriod = BasePayPeriod & {
  period_start?: string;
  period_end?: string;
  pay_date?: string;
};

type PayPeriodStatus = 'open' | 'closed' | 'future';

export interface PayPeriodSelectorProps {
  payPeriods: PayPeriod[];
  activePeriod: string;
  onPeriodChange: (periodId: string) => void;
  defaultPinned?: boolean;
}




const PayPeriodSelector: React.FC<PayPeriodSelectorProps> = ({
  payPeriods,
  activePeriod,
  onPeriodChange,
  defaultPinned = true,
}) => {
  const [isPinned, setIsPinned] = useState(defaultPinned);
  const [isVisible, setIsVisible] = useState(defaultPinned);

  // Utility: compute status for each period
  const today = new Date();

  // Compute status for each period (placeholder logic)
  function getPeriodStatus(period: PayPeriod, idx: number, arr: PayPeriod[]): PayPeriodStatus {
    const endDate = new Date(period.period_end ?? '');
    if (endDate > today) {
      // If this is the first future period, it's open
      const prev = arr[idx - 1];
      if (!prev || new Date(prev.period_end ?? '') <= today) return 'open';
      return 'future';
    }
    if (endDate.toDateString() === today.toDateString()) return 'open';
    return 'closed'; // before today
    // TODO: Replace with payslip closure logic
  }

  // Utility: format end date for display
  function formatPeriodEndDate(period: PayPeriod): string {
    return period.period_end ? new Date(period.period_end).toLocaleDateString('en-GB') : '-';
  }

  // Group periods by type (from live DB, not generated here)
  const groupedPeriods = payPeriods.reduce<Record<string, PayPeriod[]>>(
    (acc: Record<string, PayPeriod[]>, period: PayPeriod) => {
      if (!acc[period.type]) acc[period.type] = [];
      acc[period.type].push(period);
      return acc;
    },
    {}
  );

  // Sync pin state with localStorage
  useEffect(() => {
    const savedPinState = localStorage.getItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`
    );
    if (savedPinState !== null) {
      setIsPinned(savedPinState === "true");
    }
  }, []);
  useEffect(() => {
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
      isPinned.toString()
    );
  }, [isPinned]);

  // Visibility logic (hover, pin, etc)
  useEffect(() => {
    const checkVisibility = () => {
      const buttonHover =
        localStorage.getItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`
        ) === "true";
      const selfHover =
        localStorage.getItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`
        ) === "true";
      const newVisibility = isPinned || buttonHover || selfHover;
      if (isVisible !== newVisibility) {
        setIsVisible(newVisibility);
        localStorage.setItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`,
          newVisibility.toString()
        );
        const event = new CustomEvent("periodSelectorVisibilityChange", {
          detail: { visible: newVisibility },
        });
        window.dispatchEvent(event);
      }
    };
    checkVisibility();
    const interval = setInterval(checkVisibility, 200);
    return () => clearInterval(interval);
  }, [isPinned, isVisible]);

  // Mouse event handlers for localStorage hover
  const handleSelectorMouseEnter = () => {
    setIsVisible(true);
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`,
      "true"
    );
  };
  const handleSelectorMouseLeave = () => {
    setIsVisible(isPinned);
    localStorage.removeItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`
    );
  };

  // Sort period types from shortest to longest
  const sortOrder = {
    Weekly: 1,
    "2-Weekly": 2,
    "4-Weekly": 3,
    Monthly: 4,
    Quarterly: 5,
    Yearly: 6,
  };
  const sortedPeriodTypes = Object.keys(groupedPeriods).sort((a, b) => {
    return (
      (sortOrder[a as keyof typeof sortOrder] || 99) -
      (sortOrder[b as keyof typeof sortOrder] || 99)
    );
  });

  // Button style util
  const getButtonStyle = (
    period: PayPeriod,
    isActive: boolean,
  ) => {
    const commonStyles = "mx-0.25 transition-all rounded-sm";
    if (isActive)
      return cn(commonStyles, "bg-pink-500 hover:bg-emerald-600 text-white");
    // Compute status for styling
    const arr = (groupedPeriods[period.type] || []);
    const idx = arr.findIndex((p: PayPeriod) => p.id === period.id);
    const status = getPeriodStatus(period, idx, arr);
    switch (status) {
      case "open":
        return cn(
          commonStyles,
          "bg-gradient-to-t from-slate-500 to-slate-800 text-white hover:bg-slate-800 dark:bg-zinc-600",
        );
      case "closed":
        return cn(
          commonStyles,
          "bg-slate-400 text-white hover:bg-slate-800 dark:bg-zinc-800",
        );
      case "future":
        return cn(
          commonStyles,
          "bg-slate-200 text-slate-400 hover:bg-slate-800 hover:text-white dark:bg-zinc-800 dark:text-zinc-400",
        );
      default:
        return commonStyles;
    }
  };

  return (
    <div className="relative w-full" data-component-name="PayPeriodSelector">
      {/* Pin/Unpin Button */}
      <div className="flex items-center mb-2">
        <Button
          size="icon"
          variant={isPinned ? "default" : "outline"}
          onClick={() => setIsPinned((p) => !p)}
          aria-label={isPinned ? "Unpin period selector" : "Pin period selector"}
          className="mr-2"
        >
          {isPinned ? <PinIcon className="w-4 h-4" /> : <EyeOffIcon className="w-4 h-4" />}
        </Button>
        <span className="text-xs text-muted-foreground">
          {isPinned ? "Pinned" : "Auto-hide"}
        </span>
      </div>
      {/* Main selector UI */}
      <div
        className="relative w-full transition-all duration-300"
        onMouseEnter={handleSelectorMouseEnter}
        onMouseLeave={handleSelectorMouseLeave}
      >
        <div
          className={cn(
            "w-full overflow-hidden transition-all duration-300",
            isVisible ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0",
          )}
        >
          {sortedPeriodTypes.map((type) => {
            const periods = groupedPeriods[type];
            return (
              <div key={type} className="mb-1 flex last:border-b-0">
                {/* Type label */}
                <div className="bg-muted/30 flex w-20 flex-shrink-0 items-center justify-end px-2 py-1 text-xs font-normal">
                  {type}
                </div>
                {/* Period buttons */}
                <div className="flex-grow overflow-x-auto">
                  <div className="flex w-full">
                    {periods.map((period) => {
                      const isActive = activePeriod === period.id;
                      const width = `calc(100% / ${periods.length})`;
                      return (
                        <Tooltip key={period.id}>
                          <TooltipTrigger asChild>
                            <button
                              style={{ width }}
                              className={cn(
                                "border-r text-center last:border-r-0",
                                getButtonStyle(period, isActive),
                                "py-1 text-xs font-normal",
                              )}
                              onClick={() => onPeriodChange(period.id)}
                            >
                              {period.name}
                            </button>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            align="center"
                            className="border-zinc-700 bg-zinc-800 text-white"
                          >
                            <div className="text-center">
                              <div className="font-medium">
                                {period.type.replace("ly", "")} {period.name}
                              </div>
                              <div className="text-xs opacity-90">
                                Ending {formatPeriodEndDate(period)}
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PayPeriodSelector;
