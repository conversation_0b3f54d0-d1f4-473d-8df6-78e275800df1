import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';

export const employee = sqliteTable('employee', {
  // --- Core ---
  id: text('id').primaryKey().notNull(),

  // --- Personal Details ---
  title: text('title').notNull(),
  firstName: text('first_name').notNull(),
  middleName: text('middle_name'),
  lastName: text('last_name').notNull(),
  dateOfBirth: text('date_of_birth').notNull(),
  gender: text('gender').notNull().default('Male'),
  nationality: text('nationality'),
  passportNumber: text('passport_number'),
  maritalStatus: text('marital_status'),

  // --- Address ---
  address1: text('address1'),
  address2: text('address2'),
  address3: text('address3'),
  address4: text('address4'),
  postcode: text('postcode'),
  country: text('country').notNull().default('England'),

  // --- Contact Info ---
  email: text('email'),
  emailType: text('email_type').default('Personal'),
  phone: text('phone'),
  phoneType: text('phone_type').default('Mobile'),
  additionalEmails: text('additional_emails', { mode: 'json' }),
  additionalPhones: text('additional_phones', { mode: 'json' }),
  emergencyContacts: text('emergency_contacts', { mode: 'json' }),
  worksNumber: text('works_number'),

  // --- Employment Details ---
  startDate: text('start_date').notNull(),
  departments: text('departments', { mode: 'json' }),
  jobTitle: text('job_title'),
  isProtectedUnderTUPE: integer('is_protected_under_tupe', { mode: 'boolean' }).default(false),
  leaveDate: text('leave_date'),
  payrollId: text('payroll_id'),
  changeOfPayrollId: text('change_of_payroll_id'),
  contractedHours: text('contracted_hours'),
  irregularPaymentPattern: integer('irregular_payment_pattern', { mode: 'boolean' }).default(false),
  nonIndividual: integer('non_individual', { mode: 'boolean' }).default(false),

  // --- Annual Leave ---
  leaveYearStarts: text('leave_year_starts'),
  annualLeaveCalculationMethod: text('annual_leave_calculation_method'),
  annualLeaveEntitlement: integer('annual_leave_entitlement'),
  annualLeaveCarryOver: integer('annual_leave_carry_over', { mode: 'boolean' }).default(false),
  annualLeaveCarryOverValue: integer('annual_leave_carry_over_value').default(0),
  annualLeaveCarryOverUnit: text('annual_leave_carry_over_unit').default('days'),
  annualLeaveAdjustment: integer('annual_leave_adjustment', { mode: 'boolean' }).default(false),
  annualLeaveAdjustmentValue: integer('annual_leave_adjustment_value').default(0),
  annualLeaveAdjustmentUnit: text('annual_leave_adjustment_unit').default('days'),
  workingDays: text('working_days', { mode: 'json' }),

  // --- Starter / Leaver ---
  overseasEmployer: integer('overseas_employer', { mode: 'boolean' }).default(false),
  starterDeclaration: text('starter_declaration'),
  previousEmploymentPay: real('previous_employment_pay'),
  previousEmploymentTax: real('previous_employment_tax'),

  // --- Payment ---
  paymentFrequency: text('payment_frequency'), 
  payCalculationMethod: text('pay_calculation_method'),
  periodPayRate: real('period_pay_rate'),
  annualSalary: real('annual_salary'),
  startingSalary: real('starting_salary'),
  hourlyRates: text('hourly_rates', { mode: 'json' }),
  dailyRates: text('daily_rates', { mode: 'json' }),
  paymentMethod: text('payment_method'), 
  paymentDetails: text('payment_details', { mode: 'json' }),
  minimumWageProfile: text('minimum_wage_profile'),
  typicalHoursWorked: real('typical_hours_worked'),
  zeroPayHandling: text('zero_pay_handling'),

  // --- Tax / NI / RTI ---
  taxCode: text('tax_code'),
  week1Month1: integer('week1_month1', { mode: 'boolean' }).default(false),
  niTable: text('ni_table'),
  employerNIContributions: text('employer_ni_contributions'),
  niNumber: text('ni_number'),
  isDirector: integer('is_director', { mode: 'boolean' }).default(false),
  directorStartDate: text('director_start_date'),
  directorEndDate: text('director_end_date'),
  directorNICalcMethod: text('director_ni_calc_method').default('standard'), 
  isOffPayrollWorker: integer('is_off_payroll_worker', { mode: 'boolean' }).default(false),
  trivialCommutation: integer('trivial_commutation', { mode: 'boolean' }).default(false),
  flexibleDrawdown: integer('flexible_drawdown', { mode: 'boolean' }).default(false),

  // --- Student Loans ---
  studentLoan: text('student_loan'),
  studentLoanStartDate: text('student_loan_start_date'),
  studentLoanEndDate: text('student_loan_end_date'),
  postgraduateLoan: text('postgraduate_loan'),
  pgLoanStartDate: text('pg_loan_start_date'),
  pgLoanEndDate: text('pg_loan_end_date'),

  // --- Extras from DB ---
  nextReviewDate: text('next_review_date'),
  medical: text('medical'),
  notes: text('notes'),
  isConfidential: integer('is_confidential', { mode: 'boolean' }).default(false),
  enableSelfService: integer('enable_self_service', { mode: 'boolean' }).default(false),
  pdfPassword: text('pdf_password'),

});
