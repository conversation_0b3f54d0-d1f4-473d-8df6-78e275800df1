import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEmployerDBContext } from '@/providers/employer-db-provider';
import { PayPeriod } from '@/drizzle/schema/employer/payPeriod';
import { getPayPeriods, updatePayPeriod, deletePayPeriod, insertSinglePayPeriod, insertMultiplePayPeriods, deletePayPeriodsByTypeAndName } from '@/services/employerDbService';

// Fetch all pay periods for the employer DB via IPC
export function usePayPeriodsQuery() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  return useQuery<PayPeriod[]>({
    queryKey: ['payPeriods', dbPath],
    queryFn: async () => {
      if (!dbPath) return [];
      return await getPayPeriods(dbPath);
    },
    enabled: !!dbPath,
    staleTime: 5 * 60 * 1000,
  });
}

// Create a pay period via IPC
export function useCreatePayPeriodMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: Omit<PayPeriod, 'id' | 'created_at' | 'updated_at'>) => {
      if (!dbPath) {
        throw new Error("Database is not available. Please try reopening the employer file or contact support.");
      }
      const id = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const result = await insertSinglePayPeriod(dbPath, { ...data, id, created_at: now, updated_at: now });
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payPeriods', dbPath] });
    },
  });
}

// Create multiple pay periods via IPC
export function useCreatePayPeriodsMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: Omit<PayPeriod, 'id' | 'created_at' | 'updated_at'>[]) => {
      if (!dbPath) {
        throw new Error("Database is not available. Please try reopening the employer file or contact support.");
      }
      const now = Math.floor(Date.now() / 1000);
      const payPeriods = data.map(pp => ({
        ...pp,
        id: crypto.randomUUID(),
        created_at: now,
        updated_at: now,
      }));
      return await insertMultiplePayPeriods(dbPath, payPeriods);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payPeriods', dbPath] });
    },
  });
}


// Update a pay period via IPC
export function useUpdatePayPeriodMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: PayPeriod) => {
      if (!dbPath) throw new Error("Database is not available. Please try reopening the employer file or contact support.");
      return await updatePayPeriod(dbPath, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payPeriods', dbPath] });
    },
  });
}

// Delete all pay periods by type and name (schedule) via IPC
export function useDeletePayPeriodsByTypeAndNameMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ type, name }: { type: string, name: string|null }) => {
      if (!dbPath) throw new Error("Database is not available. Please try reopening the employer file or contact support.");
      return await deletePayPeriodsByTypeAndName(dbPath, type, name);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payPeriods', dbPath] });
    },
  });
}

// Delete a pay period via IPC
export function useDeletePayPeriodMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      if (!dbPath) throw new Error("Database is not available. Please try reopening the employer file or contact support.");
      return await deletePayPeriod(dbPath, id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payPeriods', dbPath] });
    },
  });
}
