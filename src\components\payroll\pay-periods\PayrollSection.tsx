import React, { useMemo, useState } from 'react';
import { usePayPeriodsQuery } from '@/hooks/tanstack-query/usePayPeriods';
import { useEmployeesQuery } from '@/hooks/tanstack-query/useEmployees';
import { PAY_PERIOD_TYPES } from '@/drizzle/schema/employer/payPeriod';
import { PayrollWizardModal } from '@/components/payroll/pay-periods/PayrollWizardModal';

export const PayrollSection: React.FC = () => {
  console.log('[PayrollSection] RENDERING', { time: new Date().toISOString() });
  const { data: employees = [] } = useEmployeesQuery();
  const { data: payPeriods = [] } = usePayPeriodsQuery();
  // Find pay frequencies in employees
  const employeeFrequencies = useMemo(() => {
    const freqs = new Set<string>();
    employees.forEach(emp => {
      if (emp.paymentFrequency) freqs.add(emp.paymentFrequency);
    });
    return Array.from(freqs) as typeof PAY_PERIOD_TYPES[number][];
  }, [employees]);

  // Find missing pay periods
  const missingFrequencies = useMemo(() => {
    const existing = new Set(payPeriods.map(p => p.type));
    return employeeFrequencies.filter(freq => !existing.has(freq));
  }, [employeeFrequencies, payPeriods]);

  return (
    <div>
      {/* ...rest of payroll UI... */}
    </div>
  );
};
