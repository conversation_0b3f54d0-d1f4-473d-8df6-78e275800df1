CREATE TABLE IF NOT EXISTS `pay_periods` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text,
	`type` text NOT NULL,
	`start_day` integer NOT NULL,
	`end_day` integer NOT NULL,
	`pay_date_rule` text NOT NULL,
	`active` integer DEFAULT true NOT NULL,
	`created_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`updated_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`notes` text
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_type_idx` ON `pay_periods` (`type`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_active_idx` ON `pay_periods` (`active`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_created_at_idx` ON `pay_periods` (`created_at`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_updated_at_idx` ON `pay_periods` (`updated_at`);--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS `pay_periods_unique_name_idx` ON `pay_periods` (`name`);--> statement-breakpoint
DROP TABLE IF EXISTS `payroll`;