import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payPeriod";
import { eq } from "drizzle-orm";

// Fetch all pay periods
ipcMain.handle("employerDb:getPayPeriods", async (event, dbPath: string) => {
  console.log("[IPC] getPayPeriods called with dbPath:", dbPath);
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) {
      console.error("[IPC] getPayPeriods: Employer DB is not open for dbPath:", dbPath);
      return { success: false, error: "Employer DB is not open" };
    }
    const db = entry.db;
    const payPeriods = await db.select().from(schema.payPeriods).all();
    console.log("[IPC] getPayPeriods: returning", payPeriods.length, "pay periods");
    return { success: true, payPeriods };
  } catch (err: any) {
    console.error("[IPC] getPayPeriods error:", err);
    return { success: false, error: err.message };
  }
});

// Add a pay period
ipcMain.handle("employerDb:addPayPeriod", async (event, dbPath: string, payPeriod: any) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    const [inserted] = await db.insert(schema.payPeriods).values(payPeriod).returning();
    return { success: true, payPeriod: inserted };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

// Add multiple pay periods (batch insert)
ipcMain.handle("employerDb:addPayPeriods", async (event, dbPath: string, payPeriods: any[]) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    const inserted = await db.insert(schema.payPeriods).values(payPeriods).returning();
    return { success: true, payPeriods: inserted };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

// Update a pay period
ipcMain.handle("employerDb:updatePayPeriod", async (event, dbPath: string, payPeriod: any) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    const [updated] = await db.update(schema.payPeriods)
      .set(payPeriod)
      .where(eq(schema.payPeriods.id, payPeriod.id))
      .returning();
    return { success: true, payPeriod: updated };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

// Delete a pay period
ipcMain.handle("employerDb:deletePayPeriod", async (event, dbPath: string, id: string) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    await db.delete(schema.payPeriods).where(eq(schema.payPeriods.id, id));
    return { success: true, id };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

import { PAY_PERIOD_TYPES } from '../../drizzle/schema/employer/payPeriod';
import { and } from 'drizzle-orm';

// Type guard for pay period type
function isPayPeriodType(type: string): type is typeof PAY_PERIOD_TYPES[number] {
  return (PAY_PERIOD_TYPES as readonly string[]).includes(type);
}

// Delete all pay periods by type and name (schedule)
ipcMain.handle("employerDb:deletePayPeriodsByTypeAndName", async (event, dbPath: string, type: string, name: string|null) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    // Validate and narrow type
    if (!isPayPeriodType(type)) {
      return { success: false, error: `Invalid period type: ${type}` };
    }
    const periodType = type;
    let whereClause;
    if (name !== null) {
      whereClause = and(
        eq(schema.payPeriods.type, periodType),
        eq(schema.payPeriods.name, name)
      );
    } else {
      whereClause = eq(schema.payPeriods.type, periodType);
    }
    const deleted = await db.delete(schema.payPeriods).where(whereClause);
    // SQLite returns the number of deleted rows (if supported by driver)
    const deletedCount = deleted.changes ?? 0;
    return { success: true, deletedCount };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});
