import { ipc<PERSON>ain } from "electron";
import { createEmployerDb } from "../file-handlers/createEmployerDb";
import { openEmployerDb, getEmployerDb } from "../main/EmployerDbManager";
import { employerSchema, type Employer } from "../../lib/schemas/employer";
import * as schema from "../../drizzle/schema/employer";
import { eq } from "drizzle-orm";

ipcMain.handle("employer:create-db", async (event, params) => {
  return await createEmployerDb(params);
});

// IPC handler to fetch employer data using persistent connection
ipcMain.handle("employer:get", async (event, dbPath: string) => {
  // Ensure DB is open
  const { db } = await openEmployerDb(dbPath);
  // There should only be one employer row per DB
  const result = await db.select().from(schema.employer).limit(1).all();
  if (!result || !result[0]) {
    throw new Error("No employer record found in DB");
  }
  // Convert nulls to undefined for Zod defaults
  const raw = result[0];
  const parsed = employerSchema.safeParse(
    Object.fromEntries(
      Object.entries(raw).map(([k, v]) => [k, v === null ? undefined : v]),
    ),
  );
  if (!parsed.success) {
    throw new Error(
      "Employer validation failed: " + JSON.stringify(parsed.error.issues),
    );
  }
  return parsed.data;
});

// IPC handler to update employer data using persistent connection
ipcMain.handle(
  "employer:update",
  async (event, dbPath: string, employer: Employer) => {
    // Validate input
    const inputParsed = employerSchema.safeParse(employer);
    if (!inputParsed.success) {
      throw new Error(
        "Employer input validation failed: " +
          JSON.stringify(inputParsed.error.issues),
      );
    }
    const { db } = await openEmployerDb(dbPath);
    const updated = await db
      .update(schema.employer)
      .set(employer)
      .where(eq(schema.employer.id, employer.id))
      .returning()
      .all();
    if (!updated || !updated[0]) {
      throw new Error("Failed to update employer record");
    }
    // Validate output
    const outputParsed = employerSchema.safeParse(
      Object.fromEntries(
        Object.entries(updated[0]).map(([k, v]) => [
          k,
          v === null ? undefined : v,
        ]),
      ),
    );
    if (!outputParsed.success) {
      throw new Error(
        "Employer output validation failed: " +
          JSON.stringify(outputParsed.error.issues),
      );
    }
    return outputParsed.data;
  },
);
