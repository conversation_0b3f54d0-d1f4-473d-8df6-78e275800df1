"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useEffect, useRef } from "react";
import { ActionToolbar } from "@/components/layouts/action-toolbar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Save, Settings } from "lucide-react";

// Import payroll components
import PayPeriodSelector from "@/components/payroll/pay-period-selector";
import { PAY_PERIOD_TYPES } from '@/drizzle/schema/employer/payPeriod';
import { PayrollWizardModal } from '@/components/payroll/pay-periods/PayrollWizardModal';
import { usePayPeriodsQuery } from '@/hooks/tanstack-query/usePayPeriods';
import PayrollOverview from "@/components/payroll/payroll-overview";
import PayslipEditor from "@/components/payroll/payslip-editor";
import BatchEditor from "@/components/payroll/batch-editor";

// Define the type for our persisted state
type PayrollState = {
  activeView: "overview" | "payslip" | "batch";
  activePeriod: string;
  selectedEmployee: string | null;
  // Overview filters and settings
  overviewStatusFilter: "all" | "open" | "closed";
  overviewSearchTerm: string;
  overviewSorting: { id: string; desc: boolean }[];
  // Batch edit filters and settings
  batchStatusFilter: "all" | "open" | "closed";
  batchSelectedColumns: string[];
  batchSorting: { id: string; desc: boolean }[];
};

// Storage key for localStorage
const PAYROLL_STATE_KEY = `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_payroll_state`;

const PayrollManagement: React.FC = () => {
  console.log('[PayrollManagement] RENDERING', { time: new Date().toISOString() });
  // State for pay period setup wizard
  const [wizardOpen, setWizardOpen] = useState(false);
  const [pendingFrequency, setPendingFrequency] = useState<typeof PAY_PERIOD_TYPES[number] | null>('monthly');
  const [wizardDismissed, setWizardDismissed] = useState(false);
  // Initialize state with values from localStorage or defaults
  const [stateInitialized, setStateInitialized] = useState(false);
  const [activeView, setActiveView] = useState<
    "overview" | "payslip" | "batch"
  >("overview");
  const [activePeriod, setActivePeriod] = useState<string>("march-2025");
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null);
  const [isPeriodSelectorVisible, setIsPeriodSelectorVisible] = useState(true);
  const { data: payPeriods, isLoading: payPeriodsLoading, error: payPeriodsError } = usePayPeriodsQuery();

  // Overview state
  const [overviewStatusFilter, setOverviewStatusFilter] = useState<
    "all" | "open" | "closed"
  >("all");
  const [overviewSearchTerm, setOverviewSearchTerm] = useState<string>("");
  const [overviewSorting, setOverviewSorting] = useState<
    { id: string; desc: boolean }[]
  >([{ id: "name", desc: false }]);

  // Batch edit state
  const [batchStatusFilter, setBatchStatusFilter] = useState<
    "all" | "open" | "closed"
  >("all");
  const [batchSelectedColumns, setBatchSelectedColumns] = useState<string[]>([
    "salary",
    "hourlyRate",
    "hours",
    "bonus",
    "deduction",
  ]);
  const [batchSorting, setBatchSorting] = useState<
    { id: string; desc: boolean }[]
  >([{ id: "name", desc: false }]);

  // Reference to the Tabs component to programmatically change tabs
  const tabsRef = useRef<HTMLDivElement>(null);

  // Load saved state from localStorage on component mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(PAYROLL_STATE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState) as PayrollState;

        // Load basic view state
        setActiveView(parsedState.activeView);
        setActivePeriod(parsedState.activePeriod);
        setSelectedEmployee(parsedState.selectedEmployee);

        // Load overview filters and settings
        if (parsedState.overviewStatusFilter) {
          setOverviewStatusFilter(parsedState.overviewStatusFilter);
        }
        if (parsedState.overviewSearchTerm) {
          setOverviewSearchTerm(parsedState.overviewSearchTerm);
        }
        if (parsedState.overviewSorting) {
          setOverviewSorting(parsedState.overviewSorting);
        }

        // Load batch edit filters and settings
        if (parsedState.batchStatusFilter) {
          setBatchStatusFilter(parsedState.batchStatusFilter);
        }
        if (parsedState.batchSelectedColumns) {
          setBatchSelectedColumns(parsedState.batchSelectedColumns);
        }
        if (parsedState.batchSorting) {
          setBatchSorting(parsedState.batchSorting);
        }
      }
    } catch (error) {
      console.error("Error loading payroll state from localStorage:", error);
    } finally {
      setStateInitialized(true);
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    // Only save after initial state is loaded to prevent overwriting with defaults
    if (stateInitialized) {
      const stateToSave: PayrollState = {
        activeView,
        activePeriod,
        selectedEmployee,
        // Overview filters and settings
        overviewStatusFilter,
        overviewSearchTerm,
        overviewSorting,
        // Batch edit filters and settings
        batchStatusFilter,
        batchSelectedColumns,
        batchSorting,
      };
      try {
        localStorage.setItem(PAYROLL_STATE_KEY, JSON.stringify(stateToSave));
      } catch (error) {
        console.error("Error saving payroll state to localStorage:", error);
      }
    }
  }, [
    activeView,
    activePeriod,
    selectedEmployee,
    overviewStatusFilter,
    overviewSearchTerm,
    overviewSorting,
    batchStatusFilter,
    batchSelectedColumns,
    batchSorting,
    stateInitialized,
  ]);

  // Handle employee selection for payslip view
  const handleEmployeeSelect = (employeeId: string) => {
    console.log("Employee selected:", employeeId);
    setSelectedEmployee(employeeId);
    setActiveView("payslip");
  };

  // Handle switching to batch edit view
  const handleSwitchToBatch = () => {
    console.log("Switching to batch edit");
    setActiveView("batch");
  };

  // Handle switching to overview
  const handleSwitchToOverview = () => {
    console.log("Switching to overview");
    setActiveView("overview");
  };

  // Effect to update the UI when activeView changes
  useEffect(() => {
    console.log("Active view changed to:", activeView);
  }, [activeView]);

  // Listen for period selector visibility changes
  useEffect(() => {
    // Check initial visibility state
    const initialVisibility =
      localStorage.getItem(
        `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`,
      ) === "true";
    setIsPeriodSelectorVisible(initialVisibility !== false); // Default to true if not set

    // Listen for visibility change events
    const handleVisibilityChange = (
      event: CustomEvent<{ visible: boolean }>,
    ) => {
      setIsPeriodSelectorVisible(event.detail.visible);
    };

    // Add event listener
    window.addEventListener(
      "periodSelectorVisibilityChange",
      handleVisibilityChange as EventListener,
    );

    // Clean up
    return () => {
      window.removeEventListener(
        "periodSelectorVisibilityChange",
        handleVisibilityChange as EventListener,
      );
    };
  }, []);

  // Don't render until state is initialized to prevent flicker
  if (!stateInitialized) {
    return <div className="w-full px-2">Loading...</div>;
  }

  return (
    <div className="flex h-full min-h-0 w-full flex-col">
      {/* Payroll Wizard Modal (auto and manual) */}
      {pendingFrequency && (
        <PayrollWizardModal
          open={wizardOpen && !wizardDismissed}
          payFrequency={pendingFrequency}
          onClose={() => {
            setWizardOpen(false);
            setWizardDismissed(true);
            setPendingFrequency(null);
          }}
        />
      )}
      {/* Main Content */}
      <Tabs
        value={activeView}
        defaultValue="overview"
        className="flex min-h-0 w-full flex-1 flex-col"
        onValueChange={(value) => setActiveView(value as any)}
      >
        {/* Pay Period Selection with proper height transition */}
        <div className={`overflow-hidden transition-all duration-300`}>
          {payPeriodsLoading ? (
            <div className="w-full px-2 py-4 text-center text-muted-foreground">
              Loading pay periods...
            </div>
          ) : payPeriodsError ? (
            <div className="w-full px-2 py-4 text-center text-red-500">
              Error loading pay periods
            </div>
          ) : payPeriods && payPeriods.length > 0 ? (
            <PayPeriodSelector
              payPeriods={payPeriods}
              activePeriod={activePeriod}
              onPeriodChange={setActivePeriod}
            />
          ) : (
            <div className="w-full px-2 py-4 text-center flex flex-col items-center gap-4">
              <span className="text-muted-foreground mb-2">No pay periods set up yet.</span>
              <Button
                variant="default"
                size="lg"
                onClick={() => {
                  setWizardDismissed(false);
                  setWizardOpen(true);
                  setPendingFrequency('monthly');
                }}
                className="w-fit"
              >
                Set up pay periods
              </Button>
            </div>
          )}
        </div>

        {/* Content container */}
        <div className="flex min-h-0 w-full flex-1 flex-col">
          <TabsContent
            value="overview"
            className="flex min-h-0 flex-1 flex-col"
          >
            <PayrollOverview
              periodId={activePeriod}
              onEmployeeSelect={handleEmployeeSelect}
              onSwitchToBatch={handleSwitchToBatch}
              onSwitchToOverview={handleSwitchToOverview}
              activeView={activeView}
              initialStatusFilter={overviewStatusFilter}
              onStatusFilterChange={setOverviewStatusFilter}
              initialSearchTerm={overviewSearchTerm}
              onSearchTermChange={setOverviewSearchTerm}
              initialSorting={overviewSorting}
              handleSortingChange={setOverviewSorting}
            />
          </TabsContent>

          <TabsContent value="payslip" className="flex min-h-0 flex-1 flex-col">
  <PayslipEditor
    periodId={activePeriod}
    employeeId={selectedEmployee}
    onBackToOverview={handleSwitchToOverview}
    onSwitchToBatch={handleSwitchToBatch}
    payPeriods={payPeriods || []}
  />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default PayrollManagement;
