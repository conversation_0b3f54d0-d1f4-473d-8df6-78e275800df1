"use client";

import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { NumberInput } from "@/components/ui/number-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Employee } from "@/lib/schemas/employee";

interface StarterLeaverSectionProps {
  employee: Employee;
  onChange: (section: string, field: string, value: any) => void;
  isNew?: boolean;
}

const StarterLeaverSection: React.FC<StarterLeaverSectionProps> = ({
  employee,
  onChange,
  isNew = false,
}) => {
  // State to track if tax code notification should be shown
  const [showTaxCodeNotification, setShowTaxCodeNotification] = useState(false);

  // Function to handle input changes
  const handleChange = (field: string, value: any) => {
    // For starter declaration changes, show the tax code notification
    if (field === "starterDeclaration" && !isNew && employee.taxCode) {
      setShowTaxCodeNotification(true);
    }

    onChange("starterLeaver", field, value);
  };

  return (
    <div className="space-y-1 pt-4">
      {/* Start Date */}
      <div className="grid grid-cols-10 items-center gap-2">
        <Label
          htmlFor="startDate"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Start date
        </Label>
        <div className="col-span-3">
          <Input
            id="startDate"
            type="date"
            className="w-full text-sm"
            value={employee.startDate}
            onChange={(e) => handleChange("startDate", e.target.value)}
            required
          />
        </div>
      </div>

      {/* Overseas Employer */}
      <div className="grid grid-cols-10 items-center gap-2 pt-4">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium">
          Overseas Employer
        </Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="overseasEmployer"
              checked={employee.overseasEmployer}
              onCheckedChange={(checked) =>
                handleChange("overseasEmployer", Boolean(checked))
              }
            />
            <Label
              htmlFor="overseasEmployer"
              className="col-span-2 text-sm font-normal"
            >
              This employment is a cross-border worker or secondment from an
              overseas employer
            </Label>
          </div>
        </div>
      </div>

      {/* Starter Declaration */}
      <div className="grid grid-cols-10 items-start gap-2 pt-6">
        <Label
          htmlFor="starterDeclaration"
          className="col-span-2 mx-4 justify-self-end pt-1 text-right font-medium"
        >
          Starter Declaration
        </Label>
        <div className="col-span-8">
          <RadioGroup
            id="starterDeclaration"
            value={employee.starterDeclaration || "A"}
            onValueChange={(value) => handleChange("starterDeclaration", value)}
            className="space-y-3"
          >
            <div className="flex items-start space-x-2">
              <RadioGroupItem
                id="starterDeclarationA"
                value="A"
                className="mt-1"
              />
              <Label
                htmlFor="starterDeclarationA"
                className="col-span-2 text-sm leading-tight font-normal"
              >
                <span className="font-medium text-emerald-600">A:</span> First
                job since 6th April and not received taxable benefits or
                pensions
              </Label>
            </div>
            <div className="flex items-start space-x-2">
              <RadioGroupItem
                id="starterDeclarationB"
                value="B"
                className="mt-1"
              />
              <Label
                htmlFor="starterDeclarationB"
                className="col-span-2 text-sm leading-tight font-normal"
              >
                <span className="font-medium text-emerald-600">B:</span> Only
                current job, but since 6 April, has had another job, or received
                taxable benefits/pensions
              </Label>
            </div>
            <div className="flex items-start space-x-2">
              <RadioGroupItem
                id="starterDeclarationC"
                value="C"
                className="mt-1"
              />
              <Label
                htmlFor="starterDeclarationC"
                className="col-span-2 text-sm leading-tight font-normal"
              >
                <span className="font-medium text-emerald-600">C:</span>{" "}
                Second/additional job or receives a State / Occupational
                Pension.
              </Label>
            </div>
          </RadioGroup>

          {/* Notification about tax code changes */}
          {showTaxCodeNotification && (
            <div className="mt-2 ml-6 text-sm text-red-500">
              The tax code has been updated based on this selection. Please
              review the TAX, NIC, RTI section.
            </div>
          )}
        </div>
      </div>

      {/* Previous Employment Details - Show only when B or C is selected */}
      {(employee.starterDeclaration === "B" ||
        employee.starterDeclaration === "C") && (
        <div className="grid grid-cols-10 items-center gap-2 pt-4">
          <Label className="col-span-2 mx-4 justify-self-end text-right font-medium whitespace-nowrap">
            Previous employment
          </Label>
          <div className="col-span-8 flex items-center gap-4">
            <div className="flex items-center">
              <Label className="mr-2 text-sm whitespace-nowrap">
                Gross taxable pay £
              </Label>
              <NumberInput
                decimalPlaces={2}
                className="w-32 text-sm"
                value={employee.previousEmploymentPay ?? undefined}
                onChange={(value) =>
                  handleChange("previousEmploymentPay", value)
                }
              />
            </div>
            <div className="flex items-center">
              <Label className="mr-2 text-sm whitespace-nowrap">
                Gross tax £
              </Label>
              <NumberInput
                decimalPlaces={2}
                className="w-32 text-sm"
                value={employee.previousEmploymentTax ?? undefined}
                onChange={(value) =>
                  handleChange("previousEmploymentTax", value)
                }
              />
            </div>
          </div>
        </div>
      )}

      {/* Leaver Information */}
      <div className="grid grid-cols-10 items-center gap-2 pt-6">
        <Label
          htmlFor="leaveDate"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Leave date
        </Label>
        <div className="col-span-2">
          <Input
            id="leaveDate"
            type="date"
            className="w-full text-sm"
            placeholder="Leave date"
            value={employee.leaveDate || ""}
            onChange={(e) => handleChange("leaveDate", e.target.value)}
          />
        </div>
      </div>
    </div>
  );
};

export default StarterLeaverSection;
