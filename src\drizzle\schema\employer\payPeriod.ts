import { sqliteTable, text, integer, primaryKey, index, uniqueIndex } from 'drizzle-orm/sqlite-core';
import { z } from 'zod';
import { sql } from 'drizzle-orm';

// Enum for pay period types
export const PAY_PERIOD_TYPES = [
  'weekly',
  'two_weekly',
  'four_weekly',
  'monthly',
  'quarterly',
  'yearly',
] as const;

export const payPeriodTypeEnum = z.enum(PAY_PERIOD_TYPES);

export const payPeriods = sqliteTable(
  'pay_periods',
  {
    id: text('id').primaryKey().notNull(), // UUID
    name: text('name'), // nullable, custom naming
    type: text('type', { enum: PAY_PERIOD_TYPES }).notNull(),
    start_day: integer('start_day').notNull(), // e.g. 1–31 or 0–6
    end_day: integer('end_day').notNull(),
    pay_date_rule: text('pay_date_rule', { mode: 'json' }).notNull(), // JSON
    active: integer('active', { mode: 'boolean' }).notNull().default(true),
    created_at: integer('created_at').notNull().default(sql`(strftime('%s','now'))`),
    updated_at: integer('updated_at').notNull().default(sql`(strftime('%s','now'))`),
    notes: text('notes'),
  },
  (table) => ({
    typeIdx: index('pay_periods_type_idx').on(table.type),
    activeIdx: index('pay_periods_active_idx').on(table.active),
    createdAtIdx: index('pay_periods_created_at_idx').on(table.created_at),
    updatedAtIdx: index('pay_periods_updated_at_idx').on(table.updated_at),
    uniqueNameIdx: uniqueIndex('pay_periods_unique_name_idx').on(table.name),
  })
);

export const payPeriodSchema = z.object({
  id: z.string().uuid(),
  name: z.string().nullable(),
  type: payPeriodTypeEnum,
  start_day: z.number().int(),
  end_day: z.number().int(),
  pay_date_rule: z.any(), // Consider a more specific Zod schema for pay date rules
  active: z.boolean(),
  created_at: z.number().int(),
  updated_at: z.number().int(),
  notes: z.string().nullable(),
});

export type PayPeriod = z.infer<typeof payPeriodSchema>;
