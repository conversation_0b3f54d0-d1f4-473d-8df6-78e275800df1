"use client";

import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Employee, defaultEmployee } from "@/lib/schemas/employee";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
// Import dialog components for unsaved changes warning
import { UnsavedChangesDialog } from "@/components/misc/unsaved-changes-dialog";
// Import with absolute paths to avoid TypeScript errors
import EmployeeList from "@/components/employees/employee-list";
import EmployeeDetails from "@/components/employees/employee-details";
import { useThemeSafeNavigation } from "@/hooks/use-theme-safe-navigation";

import { useEmployeesQuery } from "@/hooks/tanstack-query/useEmployeesQuery";
import { useEmployeesMutation } from "@/hooks/tanstack-query/useEmployeesMutation";
import { addEmployee, updateEmployee } from "@/services/employerDbService";
import { useEmployerDBContext } from "@/providers/employer-db-provider";

// Data fetching is now handled via useEmployeesQuery (TanStack Query + Drizzle ORM)

const EmployeeManagement: React.FC = () => {
  // Get active employer dbPath from context
  const { openEmployers, activeEmployerId } = useEmployerDBContext();
  const activeEmployer = openEmployers.find((e) => e.id === activeEmployerId);
  const dbPath = activeEmployer?.dbPath;

  const { data: employees = [], isLoading, error } = useEmployeesQuery();
  const employeeMutation = useEmployeesMutation();
  // const [employees, setEmployees] = useState<Employee[]>([]); // Removed local state, now using TanStack Query
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null,
  );
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [taxYearFilter, setTaxYearFilter] = useState("current");
  // Add state for tracking unsaved changes and dialog
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingEmployeeSelection, setPendingEmployeeSelection] =
    useState<Employee | null>(null);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null,
  );
  const [dialogAction, setDialogAction] = useState<
    "selectEmployee" | "navigate"
  >("selectEmployee");
  // Create a ref for the employee details component to trigger save
  const employeeDetailsRef = useRef<{
    saveChanges: () => void;
  } | null>(null);
  // Use our custom hook for theme-safe navigation
  const { navigateTo } = useThemeSafeNavigation();

  // Add event listener to intercept navigation away from the page
  useEffect(() => {
    // Only set up the listener if there are unsaved changes
    if (!hasUnsavedChanges) return;

    // Function to intercept navigation link clicks
    const interceptNavigation = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Look for clicks on navbar links or buttons
      const navItem = target.closest("a[href], button[data-nav]");

      if (navItem) {
        // If it's a main nav item (from the top navigation)
        const isMainNav = navItem.closest("nav, header");

        // Get the destination URL if it's a link
        const navUrl =
          navItem.getAttribute("href") ||
          navItem.getAttribute("data-href") ||
          "/";

        // Prevent default navigation
        event.preventDefault();
        event.stopPropagation();

        // Show the dialog and store pending navigation
        setPendingNavigation(navUrl);
        setDialogAction("navigate");
        setShowUnsavedDialog(true);
      }
    };

    // Add the event listener
    document.addEventListener("click", interceptNavigation, true);

    // Clean up
    return () => {
      document.removeEventListener("click", interceptNavigation, true);
    };
  }, [hasUnsavedChanges]);

  // Determine if an employee belongs to the current tax year
  // Parse a date in DD/MM/YYYY format as a JS Date
  const parseUKDate = (dateStr: string): Date => {
    const [day, month, year] = dateStr.split("/").map(Number);
    return new Date(year, month - 1, day);
  };

  const isCurrentTaxYear = (employee: Employee): boolean => {
    // Current tax year in UK runs from April 6 to April 5 of the next year
    const today = new Date();
    const currentYear = today.getFullYear();
    const taxYearStart = new Date(
      today.getMonth() >= 3 && today.getDate() >= 6
        ? currentYear
        : currentYear - 1,
      3,
      6,
    ); // April 6th of the current or previous year

    // Employee is in current tax year if they haven't left or left during/after the current tax year start
    const leaveDateRaw = employee.leaveDate;
    const leaveDate = leaveDateRaw ? parseUKDate(leaveDateRaw) : null;

    // Debug log

    // If they have no leave date, they are current employees
    if (!leaveDate) return true;

    // They are current tax year employees if they left after the tax year start
    return leaveDate > taxYearStart;
  };

  // Filter employees based on search query and tax year filter
  const filteredEmployees = employees.filter((employee) => {
    // Handle multi-word search by splitting the query into terms
    const searchTerms = searchQuery
      .toLowerCase()
      .split(" ")
      .filter((term) => term.trim() !== "");

    // If no search terms, consider it a match
    if (searchTerms.length === 0) return true;

    // Check if all search terms match somewhere in the employee's name
    const matchesSearch = searchTerms.every((term) => {
      const fullName =
        `${employee.firstName} ${employee.lastName}`.toLowerCase();
      return fullName.includes(term);
    });

    // Apply tax year filter
    const matchesTaxYear =
      taxYearFilter === "all" || isCurrentTaxYear(employee);

    return matchesSearch && matchesTaxYear;
  });

  // Function to handle adding a new employee
  const handleAddNew = () => {
    // If there are unsaved changes, show warning dialog
    if (hasUnsavedChanges) {
      setPendingEmployeeSelection({ ...defaultEmployee });
      setDialogAction("selectEmployee");
      setShowUnsavedDialog(true);
      return;
    }

    // No unsaved changes, proceed with adding new
    setSelectedEmployee({ ...defaultEmployee, id: "" });
    setIsAddingNew(true);
  };

  // Handle selection of an employee
  const handleEmployeeSelect = (employee: Employee) => {
    // If there are unsaved changes, show warning dialog
    if (hasUnsavedChanges) {
      setPendingEmployeeSelection(employee);
      setDialogAction("selectEmployee");
      setShowUnsavedDialog(true);
      return;
    }

    // No unsaved changes, proceed with selection
    setSelectedEmployee(employee);
    setIsAddingNew(false);
  };

  // Function to handle discarding changes
  const handleDiscardChanges = () => {
    setShowUnsavedDialog(false);

    if (dialogAction === "selectEmployee" && pendingEmployeeSelection) {
      // Handle employee selection
      setSelectedEmployee(pendingEmployeeSelection);
      setIsAddingNew(false);
      setPendingEmployeeSelection(null);
    } else if (dialogAction === "navigate" && pendingNavigation) {
      // Use our custom hook for theme-safe navigation
      navigateTo(pendingNavigation);
      setPendingNavigation(null);
    }

    // Reset unsaved changes flag
    setHasUnsavedChanges(false);
  };

  // Function to handle saving changes
  const handleSaveChanges = () => {
    // Close the dialog
    setShowUnsavedDialog(false);

    // Trigger save in the employee details component if reference exists
    if (employeeDetailsRef.current) {
      employeeDetailsRef.current.saveChanges();
    }

    if (dialogAction === "selectEmployee" && pendingEmployeeSelection) {
      // Set the selected employee after saving
      setSelectedEmployee(pendingEmployeeSelection);
      setIsAddingNew(false);
      setPendingEmployeeSelection(null);
    } else if (dialogAction === "navigate" && pendingNavigation) {
      // Use our custom hook for theme-safe navigation
      navigateTo(pendingNavigation);
      setPendingNavigation(null);
    }

    // Reset unsaved changes flag
    setHasUnsavedChanges(false);
  };

  // Function to handle continue editing
  const handleContinueEditing = () => {
    setShowUnsavedDialog(false);
  };

  // Handle save of employee with tracking of changes
  const handleSaveEmployee = async (updatedEmployee: Employee) => {
    try {
      // Use TanStack Query mutation for both add and update
      const result = await employeeMutation.mutateAsync(updatedEmployee);

      setSelectedEmployee(updatedEmployee);
      setIsAddingNew(false);
      setHasUnsavedChanges(false);
    } catch (err) {
      console.error("Error saving employee:", err);
      // Optionally, show error notification
      // e.g. toast.error(err instanceof Error ? err.message : 'Failed to save employee');
    }
  };

  // Handle cancel of employee edit with tracking of changes
  const handleCancelEdit = () => {
    if (isAddingNew) {
      setSelectedEmployee(null);
      setIsAddingNew(false);
    } else if (selectedEmployee) {
      // For existing employee, find the original from the employees array
      const originalEmployee = employees.find(
        (emp) =>
          emp.firstName === selectedEmployee.firstName &&
          emp.lastName === selectedEmployee.lastName,
      );

      // Reset to the original data
      setSelectedEmployee(originalEmployee || selectedEmployee);
    }

    // Reset unsaved changes flag after canceling
    setHasUnsavedChanges(false);
  };

  // Function to update unsaved changes status from EmployeeDetails
  const handleUnsavedChangesStatus = (hasChanges: boolean) => {
    setHasUnsavedChanges(hasChanges);
  };

  // Create the employee list component
  const employeeListComponent = (
    <div className="flex h-full w-full flex-col">
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center justify-between gap-2">
          <Input
            type="text"
            placeholder="Search employees..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-9 max-w-[250px]"
          />
          <Button
            size="sm"
            onClick={handleAddNew}
            disabled={isAddingNew}
            className={
              isAddingNew ? "cursor-not-allowed opacity-50" : "rounded-xl"
            }
          >
            + Add New
          </Button>
        </div>
        <div className="flex items-center text-sm">
          <RadioGroup
            value={taxYearFilter}
            onValueChange={setTaxYearFilter}
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-1">
              <RadioGroupItem value="current" id="current" />
              <Label htmlFor="current" className="cursor-pointer">
                Current Tax Year
              </Label>
            </div>
            <div className="flex items-center space-x-1">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all" className="cursor-pointer">
                All Tax Years
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto">
        <EmployeeList
          employees={employees}
          onSelectEmployee={handleEmployeeSelect}
          selectedEmployee={selectedEmployee}
          isAddingNew={isAddingNew}
          searchQuery={searchQuery}
          taxYearFilter={taxYearFilter}
        />
      </div>
    </div>
  );

  return (
    <div className="flex h-[calc(100vh-140px)] overflow-hidden">
      {/* Always visible employee list on the left */}
      <div className="h-full w-80 overflow-hidden">
        {isLoading ? (
          <div className="text-muted-foreground flex h-full items-center justify-center">
            Loading employees…
          </div>
        ) : error ? (
          <div className="flex h-full items-center justify-center text-red-600">
            Error loading employees
          </div>
        ) : (
          employeeListComponent
        )}
      </div>

      {/* Employee details or welcome screen on the right */}
      <div className="ml-4 flex flex-1 flex-col overflow-hidden rounded-xl border-2 pt-4 pl-4">
        {selectedEmployee ? (
          <EmployeeDetails
            employee={selectedEmployee}
            isNew={isAddingNew}
            onSave={handleSaveEmployee} // TODO: Wire up mutation to DB
            onCancel={handleCancelEdit}
            onChangesStatusUpdate={handleUnsavedChangesStatus}
            ref={employeeDetailsRef}
          />
        ) : (
          <div className="flex h-full flex-col items-center justify-center space-y-4 p-4 text-center">
            <h2 className="text-2xl font-bold">Employee Management</h2>
            <p className="text-muted-foreground">
              Select an employee from the list or add a new employee to get
              started.
            </p>
            <Button onClick={handleAddNew}>Add New Employee</Button>
          </div>
        )}
      </div>

      {/* Unsaved changes warning dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onOpenChange={setShowUnsavedDialog}
        onContinue={handleContinueEditing}
        onDiscard={handleDiscardChanges}
        onSave={handleSaveChanges}
      />
    </div>
  );
};

export default EmployeeManagement;
